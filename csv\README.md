# CSV Security Validation System

This system validates CSV files containing network flow data against security policies and provides guidance for detected issues.

## Components

### 1. Core Files
- `csv_processor.py` - Processes CSV files and converts flow data to dictionaries
- `security_validator.py` - Main validation engine with detailed security analysis
- `pipeline_validator.py` - Azure DevOps pipeline-friendly validator
- `unacceptable_values.json` - Configuration file defining unacceptable values
- `security_guidance.json` - Security guidance and recommendations

### 2. Configuration Files

#### unacceptable_values.json
Defines what values are considered unacceptable for each field:
```json
{
  "Service": {
    "blocked": ["telnet", "ftp"],
    "risky": ["ssh", "rdp"],
    "deprecated": ["http"]
  },
  "Port": {
    "blocked": ["23", "21"],
    "risky": ["22", "3389"]
  }
}
```

#### security_guidance.json
Provides security guidance for specific values:
```json
{
  "service_guidance": {
    "ssh": {
      "risk_level": "medium",
      "message": "SSH is a risky protocol when detected.",
      "recommendations": ["Enable key-based authentication"]
    }
  }
}
```

## Usage

### Local Testing
```bash
# Basic validation
python security_validator.py

# Pipeline-style validation
python pipeline_validator.py --csv Example.csv --unacceptable unacceptable_values.json --guidance security_guidance.json
```

### Azure DevOps Pipeline
```yaml
- script: |
    python pipeline_validator.py \
      --csv "$(csvFile)" \
      --unacceptable "unacceptable_values.json" \
      --guidance "security_guidance.json" \
      --fail-on-critical \
      --output-json "validation_results.json"
  displayName: 'Run Security Validation'
```

### Python Integration
```python
from csv_processor import csv_to_dict_simple
from security_validator import SecurityValidator

# Load CSV data
flow_data = csv_to_dict_simple("your_file.csv")

# Validate
validator = SecurityValidator("unacceptable_values.json", "security_guidance.json")
results = validator.validate_flow_data(flow_data)

# Check results
if results["critical_issues"] > 0:
    print("Critical security issues found!")
```

## CSV Format Expected

The system expects CSV files with the following structure:
```csv
,Source IP,Destination IP,Port,Service,Action
Flow,***********,***********0,111,https,Allow
Flow,***********,************,222,ssh,Allow
```

- First column contains "Flow" identifier
- Headers define the field names
- Each row represents a network flow

## Risk Levels

The system categorizes issues into risk levels:
- **Critical**: Immediate action required (e.g., blocked protocols)
- **High**: Significant risk (e.g., risky protocols like SSH)
- **Medium**: Moderate concern (e.g., deprecated protocols)
- **Low**: Minor issues or informational

## Pipeline Integration

### Exit Codes
- `0`: Success (no critical issues or issues below threshold)
- `1`: Failure (critical issues found or high-risk issues when `--fail-on-high` is used)

### Pipeline Variables
The validator sets these variables for downstream tasks:
- `criticalFlows`: Comma-separated list of flows with critical issues
- `highRiskFlows`: Comma-separated list of flows with high-risk issues
- `totalIssues`: Total number of flows with issues
- `criticalIssues`: Number of critical issues
- `highRiskIssues`: Number of high-risk issues

### Output Formats
- **Console**: Azure DevOps formatted output with ##[error], ##[warning] tags
- **JSON**: Detailed results exported to file for artifacts

## Customization

### Adding New Rules
1. Update `unacceptable_values.json` with new field rules
2. Add corresponding guidance in `security_guidance.json`
3. Test with your CSV data

### Custom Risk Mapping
Modify the `_map_category_to_risk()` method in `SecurityValidator` to change how categories map to risk levels.

### Pipeline Behavior
Use command-line flags to control pipeline behavior:
- `--fail-on-critical`: Fail pipeline on critical issues (default: True)
- `--fail-on-high`: Fail pipeline on high-risk issues (default: False)

## Troubleshooting

### Common Issues
1. **CSV Format**: Ensure your CSV has proper headers and "Flow" identifiers
2. **File Paths**: Check that all file paths are correct relative to working directory
3. **JSON Syntax**: Validate JSON configuration files for syntax errors

### Debug Mode
Run with Python's verbose mode for detailed debugging:
```bash
python -v security_validator.py
```

## Example Output

```
##[section]Security Validation Summary
##[command]Analyzed 4 flows, 4 flows have security issues
##[warning]🟠 HIGH: 1 high-risk issues found
##[command]🟡 MEDIUM: 4 medium-risk issues found

##[section]Security Issues by Flow
##[warning]HIGH RISK: flow_4 - 2 high-risk issues
PIPELINE FAILURE: 1 high-risk security issues found
```
