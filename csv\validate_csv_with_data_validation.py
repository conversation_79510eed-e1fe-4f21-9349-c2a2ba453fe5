#!/usr/bin/env python3
"""
Enhanced CSV Security Validator with Data Validation
- Skips first 31 rows automatically
- Validates and fixes common data format issues
- Handles comma-separated file types
- Flexible flow detection (flows, Flow, FLOW, etc.)
"""

import sys
from csv_processor import csv_to_dict_simple
from security_validator import SecurityValidator, generate_markdown_report

def validate_csv_with_data_validation(csv_file: str, skip_rows: int = 31):
    """
    Comprehensive CSV validation with data cleaning and security analysis.
    
    Args:
        csv_file: Path to CSV file
        skip_rows: Number of rows to skip (default: 31)
    """
    print(f"🔍 Enhanced CSV Security Validation")
    print("=" * 60)
    print(f"📁 File: {csv_file}")
    print(f"⏭️  Skipping first {skip_rows} rows")
    
    try:
        # Load CSV data with validation and cleaning
        print(f"\n📥 Loading and validating CSV data...")
        flow_data = csv_to_dict_simple(csv_file, skip_rows=skip_rows, validate_data=True)
        
        if not flow_data:
            print(f"\n❌ No data loaded. Troubleshooting:")
            print(f"   1. Check if file exists: {csv_file}")
            print(f"   2. Try different skip_rows values:")
            
            # Test different skip values
            for test_skip in [0, 1, 30, 31, 32, 33]:
                try:
                    test_data = csv_to_dict_simple(csv_file, skip_rows=test_skip, validate_data=False)
                    if test_data:
                        sample = next(iter(test_data.values()))
                        has_flow_col = any('flow' in str(k).lower() for k in sample.keys())
                        print(f"      skip_rows={test_skip}: {len(test_data)} flows, headers look valid: {has_flow_col}")
                except:
                    print(f"      skip_rows={test_skip}: Failed to load")
            
            return None
        
        print(f"\n✅ Successfully loaded and validated {len(flow_data)} flows")
        
        # Show detected structure
        sample_flow = next(iter(flow_data.values()))
        print(f"\n📋 Detected CSV structure:")
        print(f"   Columns: {list(sample_flow.keys())}")
        
        # Check for critical columns
        has_file_type = "File Type" in sample_flow
        has_source_ip = any("source" in k.lower() and "ip" in k.lower() for k in sample_flow.keys())
        has_service = any("service" in k.lower() or "protocol" in k.lower() for k in sample_flow.keys())
        
        print(f"   File Type column: {'✅' if has_file_type else '❌'}")
        print(f"   Source IP column: {'✅' if has_source_ip else '❌'}")
        print(f"   Service column: {'✅' if has_service else '❌'}")
        
        if not has_file_type:
            print(f"   ⚠️  No File Type column - file type validation will be skipped")
        
        # Run security validation
        print(f"\n🔍 Running security validation...")
        validator = SecurityValidator("unacceptable_values.json", "security_guidance.json")
        results = validator.validate_flow_data(flow_data)
        
        # Display comprehensive results
        print(f"\n📊 Security Validation Results:")
        print(f"   • Total flows analyzed: {results['total_flows']}")
        print(f"   • Flows with security issues: {results['flows_with_issues']}")
        print(f"   • Critical risk issues: {results['critical_issues']} 🔴")
        print(f"   • High risk issues: {results['high_risk_issues']} 🟠")
        print(f"   • Medium risk issues: {results['medium_risk_issues']} 🟡")
        print(f"   • Low risk issues: {results['low_risk_issues']} 🟢")
        
        # File type analysis (if available)
        if has_file_type:
            print(f"\n📁 File Type Security Analysis:")
            print("-" * 50)
            
            file_type_summary = {}
            critical_detections = []
            high_risk_detections = []
            
            for flow_id, flow_result in results["flow_results"].items():
                flow_data_item = flow_result.get("flow_data", {})
                file_type_field = flow_data_item.get("File Type", "")
                source_ip = flow_data_item.get("Source IP", "unknown")
                dest_ip = flow_data_item.get("Destination IP", "unknown")
                service = flow_data_item.get("Service", "unknown")
                action = flow_data_item.get("Action", "unknown")
                
                if file_type_field:
                    # Handle comma-separated file types
                    if "," in file_type_field:
                        individual_types = [ft.strip().lower() for ft in file_type_field.split(",")]
                        display_types = f"Multiple: {file_type_field}"
                    else:
                        individual_types = [file_type_field.strip().lower()]
                        display_types = file_type_field
                    
                    # Count file types
                    for file_type in individual_types:
                        if file_type:
                            file_type_summary[file_type] = file_type_summary.get(file_type, 0) + 1
                    
                    # Check for high-risk file type issues
                    file_type_issues = [issue for issue in flow_result.get("issues", []) 
                                      if issue.get("field") == "File Type"]
                    
                    if file_type_issues:
                        for issue in file_type_issues:
                            risk_level = issue.get("risk_level", "unknown")
                            value = issue.get("value", "unknown")
                            
                            detection = {
                                "flow_id": flow_id,
                                "file_type": value,
                                "display_types": display_types,
                                "risk_level": risk_level,
                                "source_ip": source_ip,
                                "dest_ip": dest_ip,
                                "service": service,
                                "action": action,
                                "message": issue.get("message", "")
                            }
                            
                            if risk_level == "critical":
                                critical_detections.append(detection)
                            elif risk_level == "high":
                                high_risk_detections.append(detection)
                    else:
                        print(f"   ✅ {flow_id}: {display_types} - No security issues")
            
            # Show file type summary
            if file_type_summary:
                print(f"\n📊 File Type Distribution:")
                sorted_types = sorted(file_type_summary.items(), key=lambda x: x[1], reverse=True)
                for file_type, count in sorted_types:
                    print(f"   • {file_type}: {count} occurrences")
            
            # Show critical detections
            if critical_detections:
                print(f"\n🔴 CRITICAL File Type Detections:")
                print("-" * 40)
                for detection in critical_detections:
                    print(f"   🔴 {detection['flow_id']}: {detection['display_types']}")
                    print(f"      {detection['source_ip']} → {detection['dest_ip']} via {detection['service']} ({detection['action']})")
                    print(f"      Risk: CRITICAL - {detection['message']}")
                    print()
            
            # Show high-risk detections
            if high_risk_detections:
                print(f"\n🟠 HIGH RISK File Type Detections:")
                print("-" * 40)
                for detection in high_risk_detections:
                    print(f"   🟠 {detection['flow_id']}: {detection['display_types']}")
                    print(f"      {detection['source_ip']} → {detection['dest_ip']} via {detection['service']} ({detection['action']})")
                    print(f"      Risk: HIGH - {detection['message']}")
                    print()
            
            if not critical_detections and not high_risk_detections:
                print(f"\n✅ No critical or high-risk file types detected")
        
        # Protocol/Service analysis
        print(f"\n🌐 Protocol/Service Analysis:")
        print("-" * 35)
        
        protocol_issues = {}
        for flow_id, flow_result in results["flow_results"].items():
            flow_data_item = flow_result.get("flow_data", {})
            service = flow_data_item.get("Service", "unknown")
            
            service_issues = [issue for issue in flow_result.get("issues", []) 
                            if issue.get("field") == "Service"]
            
            if service_issues:
                for issue in service_issues:
                    risk_level = issue.get("risk_level", "unknown")
                    if service not in protocol_issues:
                        protocol_issues[service] = {"count": 0, "max_risk": "low"}
                    
                    protocol_issues[service]["count"] += 1
                    
                    # Update max risk level
                    risk_order = {"low": 0, "medium": 1, "high": 2, "critical": 3}
                    current_max = protocol_issues[service]["max_risk"]
                    if risk_order.get(risk_level, 0) > risk_order.get(current_max, 0):
                        protocol_issues[service]["max_risk"] = risk_level
        
        if protocol_issues:
            for service, info in protocol_issues.items():
                risk_emoji = {"critical": "🔴", "high": "🟠", "medium": "🟡", "low": "🟢"}.get(info["max_risk"], "⚪")
                print(f"   {risk_emoji} {service}: {info['count']} flows, max risk: {info['max_risk']}")
        else:
            print(f"   ✅ No protocol/service security issues detected")
        
        # Generate detailed report
        print(f"\n📄 Generating comprehensive report...")
        report_file = generate_markdown_report(results, csv_file)
        print(f"✅ Detailed report saved: {report_file}")
        
        # Summary and recommendations
        total_issues = results['critical_issues'] + results['high_risk_issues']
        
        print(f"\n🎯 Summary and Recommendations:")
        if total_issues > 0:
            print(f"   ⚠️  {total_issues} critical/high-risk issues found")
            print(f"   📋 Review the detailed report: {report_file}")
            print(f"   🔍 Focus on critical (🔴) and high-risk (🟠) items first")
            
            if critical_detections:
                print(f"   🚨 IMMEDIATE ACTION REQUIRED for {len(critical_detections)} critical file type detections")
        else:
            print(f"   ✅ No critical or high-risk security issues detected")
            print(f"   📋 Review medium/low risk items in: {report_file}")
        
        return results
        
    except FileNotFoundError:
        print(f"❌ Error: File '{csv_file}' not found")
        print(f"   Make sure the file path is correct and the file exists")
        return None
        
    except Exception as e:
        print(f"❌ Error during validation: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Main function with command line support."""
    
    print("🚀 Enhanced CSV Security Validator")
    print("=" * 70)
    
    # Get CSV file from command line or user input
    if len(sys.argv) > 1:
        csv_file = sys.argv[1]
    else:
        csv_file = input("Enter CSV file path: ").strip()
    
    if not csv_file:
        print("❌ No CSV file specified")
        return
    
    # Get skip_rows from command line or use default
    skip_rows = 31
    if len(sys.argv) > 2:
        try:
            skip_rows = int(sys.argv[2])
        except ValueError:
            print(f"⚠️  Invalid skip_rows value, using default: {skip_rows}")
    
    # Run validation
    results = validate_csv_with_data_validation(csv_file, skip_rows)
    
    if results:
        print(f"\n✅ Validation completed successfully!")
        print(f"\n💡 Enhanced Features Used:")
        print(f"   🔧 Automatic data cleaning and normalization")
        print(f"   🔍 Flexible flow detection (handles 'flows', 'Flow', 'FLOW')")
        print(f"   📊 IP address and port validation")
        print(f"   🌐 Service/protocol standardization")
        print(f"   📁 File type cleaning and comma-separated support")
        print(f"   ⏭️  Automatic row skipping (first {skip_rows} rows)")
        print(f"   📋 Comprehensive security analysis and reporting")
        
        print(f"\n📖 Usage Examples:")
        print(f"   python validate_csv_with_data_validation.py your_file.csv")
        print(f"   python validate_csv_with_data_validation.py your_file.csv 32")
        
    else:
        print(f"\n❌ Validation failed!")
        print(f"\n🔧 Troubleshooting Tips:")
        print(f"   • Verify the CSV file exists and is readable")
        print(f"   • Try different skip_rows values (0, 30, 31, 32)")
        print(f"   • Ensure data rows start with 'Flow' (any case)")
        print(f"   • Check that the CSV has proper column headers")

if __name__ == "__main__":
    main()
