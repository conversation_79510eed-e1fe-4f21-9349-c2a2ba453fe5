"""
Example of how to use the CSV processing function in your script
"""

from csv_processor import csv_to_dict_simple


def main():
    # Path to your CSV file
    csv_file = "Example.csv"
    
    # Get all flow rows as dictionaries
    flow_data = csv_to_dict_simple(csv_file)
    
    # Create a main dictionary to store all your processed data
    processed_flows = {}
    
    # Iterate through each flow row
    for i, flow_row in enumerate(flow_data):
        # You can use the row index as a key, or create your own key
        flow_key = f"flow_{i+1}"
        
        # Add this flow's data to your main dictionary
        processed_flows[flow_key] = flow_row
        
        # Example: Access individual values
        source_ip = flow_row.get('Source IP')
        dest_ip = flow_row.get('Destination IP')
        port = flow_row.get('Port')
        service = flow_row.get('Service')
        action = flow_row.get('Action')
        
        print(f"Processing {flow_key}:")
        print(f"  Source: {source_ip} -> Destination: {dest_ip}")
        print(f"  Port: {port}, Service: {service}, Action: {action}")
        print()
    
    # Now you have all your data in the processed_flows dictionary
    print("All processed flows:")
    for flow_id, flow_data in processed_flows.items():
        print(f"{flow_id}: {flow_data}")
    
    # Example: Further processing based on specific criteria
    print("\nFiltering examples:")
    
    # Find all HTTPS flows
    https_flows = {k: v for k, v in processed_flows.items() 
                   if v.get('Service') == 'https'}
    print(f"HTTPS flows: {len(https_flows)}")
    
    # Find flows to specific IP range
    target_flows = {k: v for k, v in processed_flows.items() 
                    if v.get('Destination IP', '').startswith('**********')}
    print(f"Flows to **********xx: {len(target_flows)}")
    
    return processed_flows


if __name__ == "__main__":
    result = main()
