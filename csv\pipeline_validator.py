"""
Simplified security validator for Azure DevOps pipeline integration.
Returns exit codes and structured output suitable for CI/CD.
"""

import json
import sys
from typing import Dict, Any
from csv_processor import csv_to_dict_simple
from security_validator import SecurityValidator


def validate_csv_security(csv_file: str, unacceptable_file: str, guidance_file: str, 
                         fail_on_critical: bool = True, fail_on_high: bool = False) -> int:
    """
    Validate CSV security and return appropriate exit code for pipeline.
    
    Args:
        csv_file: Path to CSV file
        unacceptable_file: Path to unacceptable values JSON
        guidance_file: Path to security guidance JSON
        fail_on_critical: Whether to fail pipeline on critical issues
        fail_on_high: Whether to fail pipeline on high-risk issues
        
    Returns:
        int: Exit code (0 = success, 1 = failure)
    """
    try:
        # Load and validate data
        flow_data = csv_to_dict_simple(csv_file)
        if not flow_data:
            print("ERROR: No flow data found in CSV file")
            return 1
        
        validator = SecurityValidator(unacceptable_file, guidance_file)
        results = validator.validate_flow_data(flow_data)
        
        # Print results in pipeline-friendly format
        print_pipeline_results(results)
        
        # Determine exit code based on severity
        exit_code = 0
        
        if fail_on_critical and results["critical_issues"] > 0:
            print(f"PIPELINE FAILURE: {results['critical_issues']} critical security issues found")
            exit_code = 1
        
        if fail_on_high and results["high_risk_issues"] > 0:
            print(f"PIPELINE FAILURE: {results['high_risk_issues']} high-risk security issues found")
            exit_code = 1
        
        return exit_code
        
    except Exception as e:
        print(f"ERROR: Validation failed with exception: {e}")
        return 1


def print_pipeline_results(results: Dict[str, Any]) -> None:
    """Print results in Azure DevOps pipeline friendly format."""

    try:
        # Check if results has the expected structure
        if not isinstance(results, dict):
            print(f"##[error]Invalid results format: expected dict, got {type(results)}")
            return

        # Print any errors first
        if "errors" in results and results["errors"]:
            print("##[section]Validation Errors")
            for error in results["errors"]:
                print(f"##[error]{error}")

        # Summary for pipeline logs
        print("##[section]Security Validation Summary")
        summary_lines = results.get("summary", ["No summary available"])
        for summary_line in summary_lines:
            if "CRITICAL" in summary_line or "❌" in summary_line:
                print(f"##[error]{summary_line}")
            elif "HIGH" in summary_line or "🟠" in summary_line:
                print(f"##[warning]{summary_line}")
            else:
                print(f"##[command]{summary_line}")

        # Detailed issues for pipeline
        print("\n##[section]Security Issues by Flow")

        critical_flows = []
        high_risk_flows = []

        flow_results = results.get("flow_results", {})
        if not flow_results:
            print("##[warning]No flow results available")
            return

        for flow_id, flow_result in flow_results.items():
            if not isinstance(flow_result, dict):
                print(f"##[warning]Invalid flow result for {flow_id}")
                continue

            risk_level = flow_result.get("overall_risk", "unknown")
            issues_count = len(flow_result.get("issues", []))

            if risk_level == "critical":
                critical_flows.append(flow_id)
                print(f"##[error]CRITICAL: {flow_id} - {issues_count} critical issues")
            elif risk_level == "high":
                high_risk_flows.append(flow_id)
                print(f"##[warning]HIGH RISK: {flow_id} - {issues_count} high-risk issues")
            elif issues_count > 0:
                print(f"##[command]MEDIUM/LOW: {flow_id} - {issues_count} issues")

        # Create pipeline variables for downstream tasks
        if critical_flows:
            print(f"##vso[task.setvariable variable=criticalFlows]{','.join(critical_flows)}")
        if high_risk_flows:
            print(f"##vso[task.setvariable variable=highRiskFlows]{','.join(high_risk_flows)}")

        # Use .get() with defaults to prevent KeyError
        print(f"##vso[task.setvariable variable=totalIssues]{results.get('flows_with_issues', 0)}")
        print(f"##vso[task.setvariable variable=criticalIssues]{results.get('critical_issues', 0)}")
        print(f"##vso[task.setvariable variable=highRiskIssues]{results.get('high_risk_issues', 0)}")

    except Exception as e:
        print(f"##[error]Error printing pipeline results: {str(e)}")
        print(f"##[error]Results structure: {type(results)}")
        if isinstance(results, dict):
            print(f"##[error]Available keys: {list(results.keys())}")


def export_results_json(results: Dict[str, Any], output_file: str) -> None:
    """Export results to JSON file for pipeline artifacts."""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"##[command]Results exported to {output_file}")
    except Exception as e:
        print(f"##[warning]Failed to export results: {e}")


def main():
    """Main function for pipeline execution."""
    import argparse
    
    parser = argparse.ArgumentParser(description='CSV Security Validator for Azure DevOps')
    parser.add_argument('--csv', required=True, help='Path to CSV file')
    parser.add_argument('--unacceptable', required=True, help='Path to unacceptable values JSON')
    parser.add_argument('--guidance', required=True, help='Path to security guidance JSON')
    parser.add_argument('--fail-on-critical', action='store_true', default=True,
                       help='Fail pipeline on critical issues (default: True)')
    parser.add_argument('--fail-on-high', action='store_true', default=False,
                       help='Fail pipeline on high-risk issues (default: False)')
    parser.add_argument('--output-json', help='Export results to JSON file')
    
    args = parser.parse_args()
    
    # Run validation
    exit_code = validate_csv_security(
        csv_file=args.csv,
        unacceptable_file=args.unacceptable,
        guidance_file=args.guidance,
        fail_on_critical=args.fail_on_critical,
        fail_on_high=args.fail_on_high
    )
    
    # Export results if requested
    if args.output_json:
        try:
            flow_data = csv_to_dict_simple(args.csv)
            validator = SecurityValidator(args.unacceptable, args.guidance)
            results = validator.validate_flow_data(flow_data)
            export_results_json(results, args.output_json)
        except Exception as e:
            print(f"##[warning]Failed to export JSON: {e}")
    
    # Exit with appropriate code
    sys.exit(exit_code)


if __name__ == "__main__":
    # For local testing without arguments
    if len(sys.argv) == 1:
        print("Running local test...")
        exit_code = validate_csv_security(
            csv_file="Example.csv",
            unacceptable_file="unacceptable_values.json", 
            guidance_file="security_guidance.json",
            fail_on_critical=True,
            fail_on_high=False
        )
        print(f"\nLocal test completed with exit code: {exit_code}")
    else:
        main()
