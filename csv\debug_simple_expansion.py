#!/usr/bin/env python3
"""
Simple debug script to test expansion with your exact data.
"""

from security_validator import expand_comma_separated_flows

def test_with_your_data():
    """Test expansion with the exact values you mentioned."""
    
    print("🧪 Testing Expansion with Your Exact Data")
    print("=" * 50)
    
    # Create a test flow with your exact values
    test_flow = {
        "Source IP": "***********, ***********",  # Your exact value
        "Destination IP": "************",          # Single value
        "Port": "443",                             # Single value  
        "Service": "https",                        # Single value
        "Action": "Allow",                         # Single value
        "File Type": "pdf"                         # Single value
    }
    
    print(f"Input flow: {test_flow}")
    
    # Test the expansion function directly
    try:
        expanded_flows = expand_comma_separated_flows(test_flow, "test_flow")
        
        print(f"\n✅ Expansion completed!")
        print(f"Number of flows created: {len(expanded_flows)}")
        
        for i, flow in enumerate(expanded_flows):
            print(f"\nFlow {i+1}:")
            for field, value in flow.items():
                print(f"  {field}: '{value}'")
                
    except Exception as e:
        print(f"❌ Error during expansion: {e}")
        import traceback
        traceback.print_exc()

def test_comma_detection():
    """Test comma detection with your exact string."""
    
    print(f"\n🔍 Testing Comma Detection")
    print("=" * 35)
    
    test_value = "***********, ***********"
    
    print(f"Test value: '{test_value}'")
    print(f"Type: {type(test_value)}")
    print(f"Length: {len(test_value)}")
    print(f"Repr: {repr(test_value)}")
    
    # Test comma detection
    has_comma = ',' in test_value
    print(f"Has comma: {has_comma}")
    
    if has_comma:
        # Test splitting
        split_result = [v.strip() for v in test_value.split(',') if v.strip()]
        print(f"Split result: {split_result}")
        print(f"Split length: {len(split_result)}")
        
        # Test each split value
        for i, val in enumerate(split_result):
            print(f"  [{i}]: '{val}' (length: {len(val)})")
    else:
        print("❌ No comma detected - this is the problem!")

def test_step_by_step():
    """Step by step test of the expansion logic."""
    
    print(f"\n🔧 Step-by-Step Expansion Test")
    print("=" * 40)
    
    base_flow = {
        "Source IP": "***********, ***********",
        "Destination IP": "************",
        "Port": "443",
        "Service": "https",
        "Action": "Allow",
        "File Type": "pdf"
    }
    
    print(f"Step 1: Input flow")
    print(f"  {base_flow}")
    
    print(f"\nStep 2: Check each field for commas")
    comma_fields = {}
    
    for field, value in base_flow.items():
        print(f"  Checking '{field}' = '{value}'")
        
        if value and ',' in str(value):
            print(f"    ✅ Has comma!")
            split_values = [v.strip() for v in str(value).split(',') if v.strip()]
            print(f"    Split into: {split_values}")
            
            if len(split_values) > 1:
                comma_fields[field] = split_values
                print(f"    ✅ Added to expansion list")
            else:
                print(f"    ❌ Only one value after split")
        else:
            print(f"    ❌ No comma")
    
    print(f"\nStep 3: Fields to expand")
    print(f"  {comma_fields}")
    
    if not comma_fields:
        print(f"\nStep 4: No expansion needed - returning original flow")
        return [base_flow]
    
    print(f"\nStep 4: Generate combinations")
    import itertools
    
    field_names = list(comma_fields.keys())
    value_lists = list(comma_fields.values())
    
    print(f"  Field names: {field_names}")
    print(f"  Value lists: {value_lists}")
    
    combinations = list(itertools.product(*value_lists))
    print(f"  Combinations: {combinations}")
    
    print(f"\nStep 5: Create expanded flows")
    expanded_flows = []
    for i, combination in enumerate(combinations):
        new_flow = base_flow.copy()
        
        for field_name, new_value in zip(field_names, combination):
            new_flow[field_name] = new_value
        
        expanded_flows.append(new_flow)
        print(f"  Flow {i+1}: {new_flow}")
    
    return expanded_flows

if __name__ == "__main__":
    print("🚨 Simple Expansion Debug Tool")
    print("=" * 60)
    
    # Test 1: Comma detection
    test_comma_detection()
    
    # Test 2: Step by step
    test_step_by_step()
    
    # Test 3: Full function test
    test_with_your_data()
    
    print(f"\n💡 If this works but your CSV doesn't:")
    print(f"   1. The issue is in CSV parsing, not expansion")
    print(f"   2. Check that comma-separated values reach the expansion function")
    print(f"   3. Verify the field mapping is correct")
