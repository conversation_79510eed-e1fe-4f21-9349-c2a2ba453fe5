"""
Troubleshooting script to identify KeyError issues on different devices.
Run this on your other device to diagnose the problem.
"""

import sys
import os
import json
import traceback


def check_environment():
    """Check the environment and file availability."""
    print("=== ENVIRONMENT CHECK ===")
    print(f"Python version: {sys.version}")
    print(f"Working directory: {os.getcwd()}")
    print(f"Files in directory: {os.listdir('.')}")
    print()


def test_csv_loading():
    """Test CSV loading with different methods."""
    print("=== CSV LOADING TEST ===")
    
    csv_file = "Example.csv"
    
    # Check if file exists
    if not os.path.exists(csv_file):
        print(f"❌ CSV file not found: {csv_file}")
        return None
    
    print(f"✅ CSV file exists: {csv_file}")
    
    # Test 1: Try importing csv_processor
    try:
        from csv_processor import csv_to_dict_simple
        print("✅ csv_processor import successful")
        
        result = csv_to_dict_simple(csv_file)
        print(f"✅ csv_to_dict_simple successful: {type(result)}, {len(result) if result else 0} items")
        
        if result:
            print(f"Sample keys: {list(result.keys())[:3]}")
            first_key = list(result.keys())[0]
            print(f"Sample data: {result[first_key]}")
        
        return result
        
    except ImportError as e:
        print(f"❌ csv_processor import failed: {e}")
    except Exception as e:
        print(f"❌ csv_to_dict_simple failed: {e}")
        traceback.print_exc()
    
    # Test 2: Manual CSV processing
    try:
        import csv
        print("Trying manual CSV processing...")
        
        with open(csv_file, 'r', newline='', encoding='utf-8') as f:
            content = f.read()
            print(f"File content preview: {content[:200]}...")
        
        flow_data = {}
        with open(csv_file, 'r', newline='', encoding='utf-8') as csvfile:
            csv_reader = csv.reader(csvfile)
            headers = next(csv_reader)
            print(f"Headers: {headers}")
            
            for i, row in enumerate(csv_reader):
                print(f"Row {i}: {row}")
                if i >= 2:  # Just show first few rows
                    break
        
        print("✅ Manual CSV reading successful")
        return {}
        
    except Exception as e:
        print(f"❌ Manual CSV processing failed: {e}")
        traceback.print_exc()
    
    return None


def test_validation_structure():
    """Test the validation results structure creation."""
    print("\n=== VALIDATION STRUCTURE TEST ===")
    
    try:
        # Test creating the results structure
        validation_results = {
            "total_flows": 0,
            "flows_with_issues": 0,
            "critical_issues": 0,
            "high_risk_issues": 0,
            "medium_risk_issues": 0,
            "low_risk_issues": 0,
            "flow_results": {},
            "summary": [],
            "errors": []
        }
        
        print("✅ Basic structure created")
        print(f"Keys: {list(validation_results.keys())}")
        
        # Test accessing the problematic key
        flows_with_issues = validation_results["flows_with_issues"]
        print(f"✅ Successfully accessed 'flows_with_issues': {flows_with_issues}")
        
        # Test modifying the structure
        validation_results["flows_with_issues"] = 5
        print(f"✅ Successfully modified 'flows_with_issues': {validation_results['flows_with_issues']}")
        
        return validation_results
        
    except Exception as e:
        print(f"❌ Structure test failed: {e}")
        traceback.print_exc()
        return None


def test_security_validator():
    """Test the SecurityValidator class."""
    print("\n=== SECURITY VALIDATOR TEST ===")
    
    try:
        from security_validator import SecurityValidator
        print("✅ SecurityValidator import successful")
        
        # Test with minimal data
        test_flow_data = {
            "flow_1": {
                "Service": "https",
                "Port": "443",
                "Action": "Allow"
            }
        }
        
        validator = SecurityValidator("unacceptable_values.json", "security_guidance.json")
        print("✅ SecurityValidator created")
        
        results = validator.validate_flow_data(test_flow_data)
        print(f"✅ Validation completed: {type(results)}")
        print(f"Result keys: {list(results.keys())}")
        
        # Test the problematic key access
        flows_with_issues = results["flows_with_issues"]
        print(f"✅ Successfully accessed 'flows_with_issues': {flows_with_issues}")
        
        return results
        
    except ImportError as e:
        print(f"❌ SecurityValidator import failed: {e}")
    except KeyError as e:
        print(f"❌ KeyError in SecurityValidator: {e}")
        print("This is likely the source of your problem!")
        traceback.print_exc()
    except Exception as e:
        print(f"❌ SecurityValidator test failed: {e}")
        traceback.print_exc()
    
    return None


def test_pipeline_validator():
    """Test the pipeline validator."""
    print("\n=== PIPELINE VALIDATOR TEST ===")
    
    try:
        from pipeline_validator import validate_csv_security
        print("✅ pipeline_validator import successful")
        
        # Test with actual files
        exit_code = validate_csv_security(
            csv_file="Example.csv",
            unacceptable_file="unacceptable_values.json",
            guidance_file="security_guidance.json",
            fail_on_critical=False,
            fail_on_high=False
        )
        
        print(f"✅ Pipeline validation completed with exit code: {exit_code}")
        
    except ImportError as e:
        print(f"❌ pipeline_validator import failed: {e}")
    except Exception as e:
        print(f"❌ Pipeline validator test failed: {e}")
        traceback.print_exc()


def main():
    """Run all troubleshooting tests."""
    print("CSV SECURITY VALIDATOR TROUBLESHOOTING")
    print("=" * 50)
    
    check_environment()
    
    csv_data = test_csv_loading()
    
    structure = test_validation_structure()
    
    if csv_data is not None:
        validator_results = test_security_validator()
    
    test_pipeline_validator()
    
    print("\n=== TROUBLESHOOTING COMPLETE ===")
    print("If you see any ❌ errors above, those indicate the source of your KeyError.")
    print("Please share the output of this script for further diagnosis.")


if __name__ == "__main__":
    main()
