#!/usr/bin/env python3
"""
Debug script to diagnose why risk counts are showing as 0.
"""

import json
import os
from security_validator import SecurityValidator

def check_json_files():
    """Check if JSON files exist and have content."""
    
    print("🔍 Checking JSON Configuration Files")
    print("=" * 60)
    
    files_to_check = [
        "unacceptable_values.json",
        "security_guidance.json"
    ]
    
    for filename in files_to_check:
        print(f"\n📁 Checking {filename}:")
        
        if os.path.exists(filename):
            print(f"   ✅ File exists")
            try:
                with open(filename, 'r') as f:
                    data = json.load(f)
                print(f"   ✅ Valid JSON format")
                print(f"   📊 Keys: {list(data.keys())}")
                
                # Show some content
                for key, value in data.items():
                    if isinstance(value, dict):
                        print(f"   📋 {key}: {len(value)} items")
                        if value:
                            sample_key = list(value.keys())[0]
                            print(f"      Sample: {sample_key} = {value[sample_key]}")
                    elif isinstance(value, list):
                        print(f"   📋 {key}: {len(value)} items")
                    else:
                        print(f"   📋 {key}: {value}")
                        
            except json.JSONDecodeError as e:
                print(f"   ❌ Invalid JSON: {e}")
            except Exception as e:
                print(f"   ❌ Error reading file: {e}")
        else:
            print(f"   ❌ File does not exist")

def test_validator_initialization():
    """Test if the validator initializes correctly."""
    
    print(f"\n🔧 Testing Validator Initialization")
    print("=" * 50)
    
    try:
        validator = SecurityValidator('unacceptable_values.json', 'security_guidance.json')
        print(f"   ✅ Validator created successfully")
        
        print(f"   📊 Unacceptable values loaded: {len(validator.unacceptable_values)} fields")
        for field, rules in validator.unacceptable_values.items():
            print(f"      {field}: {len(rules)} categories")
        
        print(f"   📊 Guidance loaded: {len(validator.guidance)} sections")
        for section, content in validator.guidance.items():
            if isinstance(content, dict):
                print(f"      {section}: {len(content)} items")
            else:
                print(f"      {section}: {type(content)}")
                
    except Exception as e:
        print(f"   ❌ Error creating validator: {e}")
        return None
    
    return validator

def test_single_flow_validation(validator):
    """Test validation of a single flow that should have issues."""
    
    print(f"\n🧪 Testing Single Flow Validation")
    print("=" * 45)
    
    if not validator:
        print("   ❌ No validator available")
        return
    
    # Create a test flow that should trigger issues
    test_flow = {
        "Source IP": "***********",
        "Destination IP": "***********0", 
        "Port": "21",
        "Service": "ftp",
        "Action": "Allow",
        "File Type": "exe"
    }
    
    print(f"   🔍 Testing flow: {test_flow}")
    
    try:
        flow_result = validator._validate_single_flow("test_flow", test_flow)
        print(f"   ✅ Validation completed")
        print(f"   📊 Issues found: {len(flow_result.get('issues', []))}")
        
        for issue in flow_result.get('issues', []):
            print(f"      🔍 {issue.get('field')}: {issue.get('message')} (Risk: {issue.get('risk_level')})")
        
        print(f"   📊 Overall risk: {flow_result.get('overall_risk')}")
        
        return flow_result
        
    except Exception as e:
        print(f"   ❌ Error validating flow: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_full_validation():
    """Test full CSV validation process."""
    
    print(f"\n🔄 Testing Full CSV Validation")
    print("=" * 40)
    
    # Create a simple test CSV
    test_csv_content = """Source IP,Destination IP,Port,Service,Action,File Type
Flow 1,***********,***********0,21,ftp,Allow,exe
Flow 2,***********,************,22,ssh,Allow,bat"""
    
    with open("debug_test.csv", "w") as f:
        f.write(test_csv_content)
    
    print(f"   📁 Created test CSV: debug_test.csv")
    
    try:
        validator = SecurityValidator('unacceptable_values.json', 'security_guidance.json')
        results = validator.validate_csv_file('debug_test.csv')
        
        print(f"   ✅ Validation completed")
        print(f"   📊 Results:")
        print(f"      Total flows: {results.get('total_flows', 0)}")
        print(f"      Flows with issues: {results.get('flows_with_issues', 0)}")
        print(f"      Critical issues: {results.get('critical_issues', 0)}")
        print(f"      High risk issues: {results.get('high_risk_issues', 0)}")
        print(f"      Medium risk issues: {results.get('medium_risk_issues', 0)}")
        print(f"      Low risk issues: {results.get('low_risk_issues', 0)}")
        
        # Check individual flow results
        flow_results = results.get('flow_results', {})
        print(f"      Individual flow results: {len(flow_results)}")
        
        for flow_id, flow_result in flow_results.items():
            issues = flow_result.get('issues', [])
            print(f"         {flow_id}: {len(issues)} issues")
            for issue in issues:
                print(f"            - {issue.get('field')}: {issue.get('risk_level')} ({issue.get('message')})")
        
        # Clean up
        os.remove("debug_test.csv")
        
    except Exception as e:
        print(f"   ❌ Error in full validation: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main diagnostic function."""
    
    print("🚨 Risk Counting Diagnostic Tool")
    print("=" * 70)
    print("This tool will help diagnose why risk counts are showing as 0")
    
    # Step 1: Check JSON files
    check_json_files()
    
    # Step 2: Test validator initialization
    validator = test_validator_initialization()
    
    # Step 3: Test single flow validation
    test_single_flow_validation(validator)
    
    # Step 4: Test full validation
    test_full_validation()
    
    print(f"\n💡 Troubleshooting Tips:")
    print(f"   1. Ensure unacceptable_values.json and security_guidance.json exist")
    print(f"   2. Check that JSON files have valid content")
    print(f"   3. Verify that field names match between CSV and JSON")
    print(f"   4. Check that values in CSV match those in unacceptable_values.json")
    print(f"   5. Ensure risk mapping categories are correct")

if __name__ == "__main__":
    main()
