#!/usr/bin/env python3
"""
Test script for YAML export functionality.
Demonstrates both raw CSV data export and validation results export.
"""

from csv_processor import csv_to_dict_simple
from security_validator import SecurityValidator, export_csv_data_to_yaml, export_to_yaml

def test_yaml_exports():
    """Test both YAML export functions."""
    
    print("🔄 Testing YAML Export Functions")
    print("=" * 50)
    
    # Load CSV data
    csv_file = "Example.csv"
    print(f"📁 Loading CSV file: {csv_file}")
    flow_data = csv_to_dict_simple(csv_file)
    print(f"✅ Loaded {len(flow_data)} flows")
    
    # Test 1: Export raw CSV data to YAML
    print("\n📤 Test 1: Exporting raw CSV data to YAML...")
    raw_yaml_file = export_csv_data_to_yaml(flow_data, "raw_csv_data.yaml")
    print(f"✅ Raw CSV data exported to: {raw_yaml_file}")
    
    # Test 2: Run validation and export results to YAML
    print("\n🔍 Test 2: Running security validation...")
    validator = SecurityValidator("unacceptable_values.json", "security_guidance.json")
    results = validator.validate_flow_data(flow_data)
    print(f"✅ Validation completed - {results['flows_with_issues']} flows with issues")
    
    print("\n📤 Exporting validation results to YAML...")
    results_yaml_file = export_to_yaml(results, csv_file, "validation_results.yaml")
    print(f"✅ Validation results exported to: {results_yaml_file}")
    
    # Summary
    print("\n📋 Export Summary:")
    print(f"  • Raw CSV data: {raw_yaml_file}")
    print(f"  • Validation results: {results_yaml_file}")
    print(f"  • Total flows processed: {len(flow_data)}")
    print(f"  • Flows with security issues: {results['flows_with_issues']}")
    
    return raw_yaml_file, results_yaml_file

def demonstrate_usage():
    """Demonstrate different ways to use the YAML export functions."""
    
    print("\n" + "=" * 60)
    print("📚 YAML Export Usage Examples")
    print("=" * 60)
    
    print("""
🔧 Usage Example 1: Export raw CSV data only
```python
from csv_processor import csv_to_dict_simple
from security_validator import export_csv_data_to_yaml

# Load CSV and export to YAML
flow_data = csv_to_dict_simple("your_file.csv")
yaml_file = export_csv_data_to_yaml(flow_data, "output.yaml")
print(f"CSV data exported to: {yaml_file}")
```

🔧 Usage Example 2: Export validation results
```python
from csv_processor import csv_to_dict_simple
from security_validator import SecurityValidator, export_to_yaml

# Load, validate, and export
flow_data = csv_to_dict_simple("your_file.csv")
validator = SecurityValidator("rules.json", "guidance.json")
results = validator.validate_flow_data(flow_data)
yaml_file = export_to_yaml(results, "your_file.csv", "results.yaml")
print(f"Validation results exported to: {yaml_file}")
```

🔧 Usage Example 3: Pipeline integration
```python
# In your pipeline script
from security_validator import SecurityValidator, export_to_yaml
from csv_processor import csv_to_dict_simple

def run_security_pipeline(csv_file, rules_file, guidance_file):
    # Process and validate
    flow_data = csv_to_dict_simple(csv_file)
    validator = SecurityValidator(rules_file, guidance_file)
    results = validator.validate_flow_data(flow_data)
    
    # Export results for artifacts
    yaml_file = export_to_yaml(results, csv_file, "pipeline_results.yaml")
    
    # Return for pipeline decision making
    return results, yaml_file
```
    """)

if __name__ == "__main__":
    try:
        # Run tests
        raw_file, results_file = test_yaml_exports()
        
        # Show usage examples
        demonstrate_usage()
        
        print(f"\n✅ All tests completed successfully!")
        print(f"📁 Check the generated files:")
        print(f"   • {raw_file}")
        print(f"   • {results_file}")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
