# 🔒 CSV Security Validation Report

**Generated:** 2025-07-17 20:37:56
**CSV File:** `test_security_issues.csv`
**Validation Engine:** CSV Security Validator v1.0

---

## 📊 Executive Summary

### Overall Security Status: 🔴 **CRITICAL**
*Immediate action required*

### Key Metrics
| Metric | Value | Status |
|--------|-------|--------|
| **Total Flows Analyzed** | 13 | ℹ️ |
| **Flows with Issues** | 13 | 🔴 |
| **Compliance Rate** | 0.0% | 🔴 |
| **Critical Issues** | 14 | 🔴 |
| **High Risk Issues** | 10 | 🟠 |
| **Medium Risk Issues** | 13 | 🟡 |
| **Low Risk Issues** | 0 | ⚪ |

## 🔄 Flow Expansion Analysis

| Metric | Value | Details |
|--------|-------|---------|
| **Original CSV Rows** | 1 | Rows in source CSV |
| **Total Flows Analyzed** | 13 | After comma-separated expansion |
| **Flows from Expansion** | 12 | Created from comma-separated values |
| **Expansion Groups** | 1 | CSV rows that were expanded |

### 🔄 Expansion Details
- **flow_2**: 12 flows (flow_2_a, flow_2_b, flow_2_c, flow_2_d, flow_2_e, flow_2_f, flow_2_g, flow_2_h, flow_2_i, flow_2_j, flow_2_k, flow_2_l)

## 🎯 Risk Level Breakdown

```
Critical:  14 issues  ██████████████
High:      10 issues  ██████████
Medium:    13 issues  █████████████
Low:        0 issues  
```

## 📋 Summary
- ℹ️ Analyzed 13 flows, 13 flows have security issues
- 🔴 🔴 CRITICAL: 14 critical security issues found
- 🟠 🟠 HIGH: 10 high-risk issues found
- 🟡 🟡 MEDIUM: 13 medium-risk issues found

## 🔍 Detailed Findings

### 🔴 Critical Risk Issues

#### flow_2_a *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `tcp\22` |
| Service | `ssh` |
| Action | `Allow` |
| File Type | `exe` |

**Security Issues:**
- 🟠 **Service**: Service 'ssh' is classified as risky
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🔴 **File Type**: File Type 'exe' is classified as blocked

#### flow_2_b *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `tcp\22` |
| Service | `ssh` |
| Action | `Allow` |
| File Type | `bat` |

**Security Issues:**
- 🟠 **Service**: Service 'ssh' is classified as risky
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🔴 **File Type**: File Type 'bat' is classified as blocked

#### flow_2_d *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `tcp\22` |
| Service | `ftp` |
| Action | `Allow` |
| File Type | `exe` |

**Security Issues:**
- 🔴 **Service**: Service 'ftp' is classified as blocked
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🔴 **File Type**: File Type 'exe' is classified as blocked

#### flow_2_e *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `tcp\22` |
| Service | `ftp` |
| Action | `Allow` |
| File Type | `bat` |

**Security Issues:**
- 🔴 **Service**: Service 'ftp' is classified as blocked
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🔴 **File Type**: File Type 'bat' is classified as blocked

#### flow_2_f *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `tcp\22` |
| Service | `ftp` |
| Action | `Allow` |
| File Type | `vbs` |

**Security Issues:**
- 🔴 **Service**: Service 'ftp' is classified as blocked
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🟠 **File Type**: File Type 'vbs' is classified as risky

#### flow_2_g *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `tcp\21` |
| Service | `ssh` |
| Action | `Allow` |
| File Type | `exe` |

**Security Issues:**
- 🟠 **Service**: Service 'ssh' is classified as risky
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🔴 **File Type**: File Type 'exe' is classified as blocked

#### flow_2_h *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `tcp\21` |
| Service | `ssh` |
| Action | `Allow` |
| File Type | `bat` |

**Security Issues:**
- 🟠 **Service**: Service 'ssh' is classified as risky
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🔴 **File Type**: File Type 'bat' is classified as blocked

#### flow_2_j *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `tcp\21` |
| Service | `ftp` |
| Action | `Allow` |
| File Type | `exe` |

**Security Issues:**
- 🔴 **Service**: Service 'ftp' is classified as blocked
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🔴 **File Type**: File Type 'exe' is classified as blocked

#### flow_2_k *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `tcp\21` |
| Service | `ftp` |
| Action | `Allow` |
| File Type | `bat` |

**Security Issues:**
- 🔴 **Service**: Service 'ftp' is classified as blocked
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🔴 **File Type**: File Type 'bat' is classified as blocked

#### flow_2_l *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `tcp\21` |
| Service | `ftp` |
| Action | `Allow` |
| File Type | `vbs` |

**Security Issues:**
- 🔴 **Service**: Service 'ftp' is classified as blocked
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🟠 **File Type**: File Type 'vbs' is classified as risky

### 🟠 High Risk Issues

#### flow_2_c *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `tcp\22` |
| Service | `ssh` |
| Action | `Allow` |
| File Type | `vbs` |

**Security Issues:**
- 🟠 **Service**: Service 'ssh' is classified as risky
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🟠 **File Type**: File Type 'vbs' is classified as risky

#### flow_2_i *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `tcp\21` |
| Service | `ssh` |
| Action | `Allow` |
| File Type | `vbs` |

**Security Issues:**
- 🟠 **Service**: Service 'ssh' is classified as risky
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🟠 **File Type**: File Type 'vbs' is classified as risky

### 🟡 Medium Risk Issues

#### flow_1

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `************` |
| Port | `tcp\443` |
| Service | `https` |
| Action | `Allow` |
| File Type | `pdf` |

**Security Issues:**
- 🟡 **Action**: Action 'Allow' is classified as concerning


---
*Report generated by CSV Security Validator - 2025-07-17 20:37:56*
