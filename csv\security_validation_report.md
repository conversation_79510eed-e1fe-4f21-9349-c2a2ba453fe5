# 🔒 CSV Security Validation Report

**Generated:** 2025-07-03 18:55:55
**CSV File:** `Example_with_31_header_rows.csv`
**Validation Engine:** CSV Security Validator v1.0

---

## 📊 Executive Summary

### Overall Security Status: 🔴 **CRITICAL**
*Immediate action required*

### Key Metrics
| Metric | Value | Status |
|--------|-------|--------|
| **Total Flows Analyzed** | 5 | ℹ️ |
| **Flows with Issues** | 5 | 🔴 |
| **Compliance Rate** | 0.0% | 🔴 |
| **Critical Issues** | 5 | 🔴 |
| **High Risk Issues** | 3 | 🟠 |
| **Medium Risk Issues** | 7 | 🟡 |
| **Low Risk Issues** | 0 | ⚪ |

## 🎯 Risk Level Breakdown

```
Critical:   5 issues  █████
High:       3 issues  ███
Medium:     7 issues  ███████
Low:        0 issues  
```

## 📋 Summary
- ℹ️ Analyzed 5 flows, 5 flows have security issues
- 🔴 🔴 CRITICAL: 5 critical security issues found
- 🟠 🟠 HIGH: 3 high-risk issues found
- 🟡 🟡 MEDIUM: 7 medium-risk issues found

## 🔍 Detailed Findings

### 🔴 Critical Risk Issues

#### flow_2

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `222` |
| Service | `https` |
| Action | `Allow` |
| File Type | `exe` |

**Security Issues:**
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🔴 **File Type**: File Type 'exe' is classified as blocked

#### flow_4

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `444` |
| Service | `ftp` |
| Action | `Allow` |
| File Type | `exe,dll` |

**Security Issues:**
- 🔴 **Service**: Service 'ftp' is classified as blocked
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🔴 **File Type**: File Type 'exe' (from 'exe,dll') is classified as blocked
- 🟠 **File Type**: File Type 'dll' (from 'exe,dll') is classified as risky

#### flow_5

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `555` |
| Service | `telnet` |
| Action | `Allow` |
| File Type | `bat,vbs,ps1` |

**Security Issues:**
- 🔴 **Service**: Service 'telnet' is classified as blocked
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🔴 **File Type**: File Type 'bat' (from 'bat,vbs,ps1') is classified as blocked
- 🟠 **File Type**: File Type 'vbs' (from 'bat,vbs,ps1') is classified as risky
- 🟡 **File Type**: File Type 'ps1' (from 'bat,vbs,ps1') is classified as concerning

### 🟠 High Risk Issues

#### flow_3

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `333` |
| Service | `ssh` |
| Action | `Allow` |
| File Type | `zip` |

**Security Issues:**
- 🟠 **Service**: Service 'ssh' is classified as risky
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🟡 **File Type**: File Type 'zip' is classified as concerning

### 🟡 Medium Risk Issues

#### flow_1

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `************` |
| Port | `111` |
| Service | `https` |
| Action | `Allow` |
| File Type | `pdf` |

**Security Issues:**
- 🟡 **Action**: Action 'Allow' is classified as concerning


---
*Report generated by CSV Security Validator - 2025-07-03 18:55:55*
