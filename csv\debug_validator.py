"""
Debug version of the security validator to help identify KeyError issues.
"""

import json
import traceback
from typing import Dict, Any
from csv_processor import csv_to_dict_simple


def debug_validate_csv(csv_file: str, unacceptable_file: str = None, guidance_file: str = None):
    """
    Debug version that prints detailed information at each step.
    """
    print("=== DEBUG VALIDATION START ===")
    
    try:
        # Step 1: Load CSV data
        print(f"Step 1: Loading CSV file: {csv_file}")
        flow_data = csv_to_dict_simple(csv_file)
        print(f"CSV loaded successfully. Type: {type(flow_data)}")
        print(f"Number of flows: {len(flow_data) if flow_data else 0}")
        
        if flow_data:
            print("Sample flow keys:", list(flow_data.keys())[:3])
            first_key = list(flow_data.keys())[0]
            print(f"Sample flow data: {flow_data[first_key]}")
        else:
            print("❌ No flow data loaded!")
            return None
            
    except Exception as e:
        print(f"❌ Error loading CSV: {e}")
        traceback.print_exc()
        return None
    
    try:
        # Step 2: Load configuration files
        print(f"\nStep 2: Loading configuration files")
        
        unacceptable_values = {}
        if unacceptable_file:
            print(f"Loading unacceptable values from: {unacceptable_file}")
            try:
                with open(unacceptable_file, 'r', encoding='utf-8') as f:
                    unacceptable_values = json.load(f)
                print(f"Unacceptable values loaded. Keys: {list(unacceptable_values.keys())}")
            except Exception as e:
                print(f"⚠️ Warning: Could not load unacceptable values: {e}")
        
        guidance = {}
        if guidance_file:
            print(f"Loading guidance from: {guidance_file}")
            try:
                with open(guidance_file, 'r', encoding='utf-8') as f:
                    guidance = json.load(f)
                print(f"Guidance loaded. Keys: {list(guidance.keys())}")
            except Exception as e:
                print(f"⚠️ Warning: Could not load guidance: {e}")
                
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        traceback.print_exc()
        return None
    
    try:
        # Step 3: Initialize results structure
        print(f"\nStep 3: Initializing validation results")
        
        validation_results = {
            "total_flows": len(flow_data),
            "flows_with_issues": 0,
            "critical_issues": 0,
            "high_risk_issues": 0,
            "medium_risk_issues": 0,
            "low_risk_issues": 0,
            "flow_results": {},
            "summary": [],
            "errors": [],
            "debug_info": {
                "csv_file": csv_file,
                "unacceptable_file": unacceptable_file,
                "guidance_file": guidance_file,
                "flow_data_type": str(type(flow_data)),
                "flow_count": len(flow_data)
            }
        }
        
        print("✅ Results structure initialized")
        print(f"Initial structure keys: {list(validation_results.keys())}")
        
    except Exception as e:
        print(f"❌ Error initializing results: {e}")
        traceback.print_exc()
        return None
    
    try:
        # Step 4: Process each flow
        print(f"\nStep 4: Processing flows")
        
        for i, (flow_id, flow_info) in enumerate(flow_data.items()):
            print(f"Processing flow {i+1}/{len(flow_data)}: {flow_id}")
            
            try:
                # Simple validation for each flow
                flow_result = {
                    "flow_id": flow_id,
                    "flow_data": flow_info,
                    "issues": [],
                    "guidance": [],
                    "overall_risk": "low"
                }
                
                # Basic validation - check for SSH
                service = flow_info.get('Service', '').lower()
                if service == 'ssh':
                    flow_result["issues"].append({
                        "field": "Service",
                        "value": service,
                        "risk_level": "high",
                        "message": "SSH detected - risky protocol"
                    })
                    flow_result["overall_risk"] = "high"
                    validation_results["high_risk_issues"] += 1
                    validation_results["flows_with_issues"] += 1
                
                # Check for Allow action
                action = flow_info.get('Action', '').lower()
                if action == 'allow':
                    flow_result["issues"].append({
                        "field": "Action", 
                        "value": action,
                        "risk_level": "medium",
                        "message": "Allow action requires review"
                    })
                    if flow_result["overall_risk"] == "low":
                        flow_result["overall_risk"] = "medium"
                    validation_results["medium_risk_issues"] += 1
                    if not any(issue.get("risk_level") == "high" for issue in flow_result["issues"]):
                        validation_results["flows_with_issues"] += 1
                
                validation_results["flow_results"][flow_id] = flow_result
                
            except Exception as e:
                error_msg = f"Error processing flow {flow_id}: {e}"
                print(f"⚠️ {error_msg}")
                validation_results["errors"].append(error_msg)
        
        print(f"✅ Processed {len(validation_results['flow_results'])} flows")
        
    except Exception as e:
        print(f"❌ Error processing flows: {e}")
        traceback.print_exc()
        validation_results["errors"].append(f"Flow processing error: {e}")
    
    try:
        # Step 5: Generate summary
        print(f"\nStep 5: Generating summary")
        
        summary = []
        total = validation_results["total_flows"]
        issues = validation_results["flows_with_issues"]
        
        summary.append(f"Analyzed {total} flows, {issues} flows have security issues")
        
        if validation_results["critical_issues"] > 0:
            summary.append(f"🔴 CRITICAL: {validation_results['critical_issues']} critical issues")
        if validation_results["high_risk_issues"] > 0:
            summary.append(f"🟠 HIGH: {validation_results['high_risk_issues']} high-risk issues")
        if validation_results["medium_risk_issues"] > 0:
            summary.append(f"🟡 MEDIUM: {validation_results['medium_risk_issues']} medium-risk issues")
        if issues == 0:
            summary.append("✅ No security issues detected")
        
        validation_results["summary"] = summary
        print("✅ Summary generated")
        
    except Exception as e:
        print(f"❌ Error generating summary: {e}")
        traceback.print_exc()
        validation_results["summary"] = [f"Error generating summary: {e}"]
    
    print(f"\n=== DEBUG VALIDATION COMPLETE ===")
    print(f"Final results keys: {list(validation_results.keys())}")
    print(f"flows_with_issues: {validation_results.get('flows_with_issues', 'KEY_MISSING')}")
    
    return validation_results


def main():
    """Test the debug validator."""
    print("Running debug validation...")
    
    results = debug_validate_csv(
        csv_file="Example.csv",
        unacceptable_file="unacceptable_values.json",
        guidance_file="security_guidance.json"
    )
    
    if results:
        print(f"\n=== FINAL RESULTS ===")
        print(f"Total flows: {results.get('total_flows', 'MISSING')}")
        print(f"Flows with issues: {results.get('flows_with_issues', 'MISSING')}")
        print(f"Critical issues: {results.get('critical_issues', 'MISSING')}")
        print(f"High risk issues: {results.get('high_risk_issues', 'MISSING')}")
        print(f"Medium risk issues: {results.get('medium_risk_issues', 'MISSING')}")
        
        if results.get("errors"):
            print(f"\nErrors encountered:")
            for error in results["errors"]:
                print(f"  - {error}")
        
        print(f"\nSummary:")
        for line in results.get("summary", []):
            print(f"  {line}")
    else:
        print("❌ Validation failed completely")


if __name__ == "__main__":
    main()
