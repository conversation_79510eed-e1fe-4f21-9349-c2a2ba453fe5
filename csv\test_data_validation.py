#!/usr/bin/env python3
"""
Test script for CSV data validation and automatic fixing.
"""

from csv_processor import csv_to_dict_simple
from security_validator import SecurityValidator

def create_messy_test_csv():
    """Create a test CSV with common data format issues."""
    
    csv_content = [
        "# Test CSV with data format issues",
        "# This simulates real-world messy data",
        "",
        ",Source IP,Destination IP,Port,Service,Action,File Type",
        "flows,  ***********  ,***********0,111,HTTPS,allow,PDF",
        "Flow,ip:***********,addr:***********0,port:222,http,ALLOWED,.exe",
        "FLOW,*********** (internal),***********0,333 (ssh),ssh,Accept,zip",
        "flow,***********,***********0,444,FTP,deny,.dll,.bat",
        "Flow,***********,***********0,555,telnet,BLOCK,  .vbs, .ps1  ",
        "flows,*************,*************,80,HTTP,permit,doc",
        "Flow,***********,***********,443,https,reject,.exe,.dll,.zip"
    ]
    
    with open("messy_test_data.csv", "w", encoding="utf-8") as f:
        for line in csv_content:
            f.write(line + "\n")
    
    print("✅ Created messy test CSV with common data issues:")
    print("  • Mixed case in Flow column (flows, Flow, FLOW)")
    print("  • Extra spaces and prefixes in IP addresses")
    print("  • Port numbers with text")
    print("  • Mixed case services and actions")
    print("  • File extensions with dots and extra spaces")
    print("  • Comma-separated file types with formatting issues")

def test_data_validation():
    """Test the data validation and fixing functionality."""
    
    print("🔍 Testing CSV Data Validation and Fixing")
    print("=" * 60)
    
    # Create test file
    create_messy_test_csv()
    
    print(f"\n📁 Loading messy CSV with validation enabled...")
    
    # Test with validation enabled (default)
    flow_data_validated = csv_to_dict_simple("messy_test_data.csv", skip_rows=3, validate_data=True)
    
    if not flow_data_validated:
        print("❌ No data loaded")
        return
    
    print(f"\n📊 Validation Results:")
    print(f"✅ Successfully loaded and validated {len(flow_data_validated)} flows")
    
    # Show sample of cleaned data
    print(f"\n📋 Sample of cleaned data:")
    for i, (flow_id, flow_data) in enumerate(list(flow_data_validated.items())[:3]):
        print(f"\n  {flow_id}:")
        for field, value in flow_data.items():
            print(f"    {field}: '{value}'")
    
    # Test security validation on cleaned data
    print(f"\n🔍 Running security validation on cleaned data...")
    validator = SecurityValidator("unacceptable_values.json", "security_guidance.json")
    results = validator.validate_flow_data(flow_data_validated)
    
    print(f"\n📊 Security Validation Results:")
    print(f"  • Total flows: {results['total_flows']}")
    print(f"  • Flows with issues: {results['flows_with_issues']}")
    print(f"  • Critical issues: {results['critical_issues']} 🔴")
    print(f"  • High risk issues: {results['high_risk_issues']} 🟠")
    print(f"  • Medium risk issues: {results['medium_risk_issues']} 🟡")
    
    # Show file type detections
    print(f"\n📁 File Type Detections (after cleaning):")
    for flow_id, flow_result in results["flow_results"].items():
        flow_data_item = flow_result.get("flow_data", {})
        file_type = flow_data_item.get("File Type", "N/A")
        source_ip = flow_data_item.get("Source IP", "N/A")
        service = flow_data_item.get("Service", "N/A")
        action = flow_data_item.get("Action", "N/A")
        
        # Check for file type issues
        file_type_issues = [issue for issue in flow_result.get("issues", []) 
                          if issue.get("field") == "File Type"]
        
        if file_type_issues:
            for issue in file_type_issues:
                risk_level = issue.get("risk_level", "unknown")
                value = issue.get("value", "unknown")
                
                risk_emoji = {
                    "critical": "🔴",
                    "high": "🟠", 
                    "medium": "🟡",
                    "low": "🟢"
                }.get(risk_level, "⚪")
                
                print(f"  {risk_emoji} {flow_id}: {value} file from {source_ip} via {service} ({action})")
        else:
            print(f"  ✅ {flow_id}: {file_type} file from {source_ip} via {service} ({action})")
    
    # Compare with non-validated data
    print(f"\n🔄 Comparing with non-validated data...")
    flow_data_raw = csv_to_dict_simple("messy_test_data.csv", skip_rows=3, validate_data=False)
    
    if flow_data_raw:
        print(f"\n📋 Raw data (before validation) sample:")
        sample_flow_raw = next(iter(flow_data_raw.values()))
        sample_flow_clean = next(iter(flow_data_validated.values()))
        
        for field in sample_flow_raw.keys():
            raw_value = sample_flow_raw.get(field, "")
            clean_value = sample_flow_clean.get(field, "")
            
            if raw_value != clean_value:
                print(f"  🔧 {field}: '{raw_value}' → '{clean_value}'")
            else:
                print(f"  ✅ {field}: '{raw_value}' (no change needed)")
    
    return results

def test_validation_edge_cases():
    """Test validation with various edge cases."""
    
    print(f"\n🧪 Testing Edge Cases")
    print("=" * 30)
    
    # Create edge case CSV
    edge_cases = [
        ",Source IP,Destination IP,Port,Service,Action,File Type",
        "Flow,999.999.999.999,***********0,99999,unknown,maybe,unknown",
        "Flow,***********,not.an.ip,abc,HTTP,allow,",
        "Flow,,***********0,0,ssh,DENY,exe;dll;bat",
        "Flow,***********,***********0,-1,ftp,allow,file.exe.backup"
    ]
    
    with open("edge_cases.csv", "w", encoding="utf-8") as f:
        for line in edge_cases:
            f.write(line + "\n")
    
    print("📁 Testing edge cases...")
    flow_data = csv_to_dict_simple("edge_cases.csv", skip_rows=0, validate_data=True)
    
    if flow_data:
        print(f"✅ Loaded {len(flow_data)} flows with edge cases")
        
        # Show how edge cases were handled
        for flow_id, flow_data_item in flow_data.items():
            print(f"\n  {flow_id}:")
            for field, value in flow_data_item.items():
                print(f"    {field}: '{value}'")
    else:
        print("❌ No data loaded from edge cases")

def main():
    """Main test function."""
    
    print("🚀 CSV Data Validation Test Suite")
    print("=" * 70)
    
    # Test main validation functionality
    results = test_data_validation()
    
    # Test edge cases
    test_validation_edge_cases()
    
    if results:
        print(f"\n✅ All tests completed!")
        print(f"\n💡 Key Features Demonstrated:")
        print(f"  🔧 Automatic data cleaning and normalization")
        print(f"  🔍 Flexible flow detection (flows, Flow, FLOW)")
        print(f"  📊 IP address validation and cleaning")
        print(f"  🔢 Port number extraction and validation")
        print(f"  🌐 Service/protocol normalization")
        print(f"  ⚡ Action standardization")
        print(f"  📁 File type cleaning (removes dots, handles commas)")
        print(f"  📋 Comprehensive validation reporting")
        
        print(f"\n🎯 Benefits:")
        print(f"  • Handles messy real-world CSV data automatically")
        print(f"  • Fixes common formatting issues without manual intervention")
        print(f"  • Provides detailed feedback on what was fixed")
        print(f"  • Ensures consistent data format for security validation")
        print(f"  • Reduces false negatives in security detection")
    else:
        print(f"\n❌ Tests failed!")

if __name__ == "__main__":
    main()
