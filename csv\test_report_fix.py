#!/usr/bin/env python3
"""
Test script to verify the markdown report generation fix.
"""

from security_validator import SecurityValidator, generate_markdown_report, generate_markdown_report_legacy

def test_report_generation():
    """Test both new and legacy report generation functions."""
    
    print("🧪 Testing Markdown Report Generation Fix")
    print("=" * 60)
    
    # Create a simple test case
    validator = SecurityValidator('unacceptable_values.json', 'security_guidance.json')
    
    # Test with a simple CSV
    print("\n📊 Testing with Example.csv...")
    try:
        results = validator.validate_csv_file('Example.csv')
        print("✅ validate_csv_file completed successfully")
        
        # Test new function signature (should work)
        print("\n🔍 Testing new generate_markdown_report signature...")
        flow_data = {"flow_1": {"Source IP": "***********", "Service": "https"}}
        report_file = generate_markdown_report(results, flow_data, "Example.csv", "test_new_report.md")
        print(f"✅ New signature works: {report_file}")
        
        # Test legacy function (should work)
        print("\n🔍 Testing legacy generate_markdown_report_legacy...")
        legacy_report_file = generate_markdown_report_legacy(results, "Example.csv", "test_legacy_report.md")
        print(f"✅ Legacy signature works: {legacy_report_file}")
        
        # Test new function with None flow_data (should work)
        print("\n🔍 Testing new signature with None flow_data...")
        none_report_file = generate_markdown_report(results, None, "Example.csv", "test_none_report.md")
        print(f"✅ None flow_data works: {none_report_file}")
        
        print(f"\n✅ All report generation tests passed!")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

def test_error_handling():
    """Test error handling for invalid flow_data."""
    
    print(f"\n🔧 Testing Error Handling")
    print("=" * 40)
    
    # Create mock results
    mock_results = {
        "total_flows": 1,
        "flows_with_issues": 0,
        "critical_issues": 0,
        "high_risk_issues": 0,
        "medium_risk_issues": 0,
        "low_risk_issues": 0,
        "flow_results": {},
        "summary": ["Test summary"]
    }
    
    # Test with string instead of dict (should handle gracefully)
    print("\n🔍 Testing with string flow_data (should handle gracefully)...")
    try:
        report_file = generate_markdown_report(mock_results, "invalid_string_data", "test.csv", "test_error_report.md")
        print(f"✅ Handled string flow_data gracefully: {report_file}")
    except Exception as e:
        print(f"❌ Failed to handle string flow_data: {e}")
    
    # Test with empty dict
    print("\n🔍 Testing with empty dict flow_data...")
    try:
        report_file = generate_markdown_report(mock_results, {}, "test.csv", "test_empty_report.md")
        print(f"✅ Handled empty dict flow_data: {report_file}")
    except Exception as e:
        print(f"❌ Failed to handle empty dict: {e}")

if __name__ == "__main__":
    test_report_generation()
    test_error_handling()
    
    print(f"\n💡 Summary:")
    print(f"   • Fixed AttributeError for flow_data.keys()")
    print(f"   • Added backward compatibility for old function signature")
    print(f"   • Enhanced error handling for invalid flow_data types")
    print(f"   • Report generation now works with comma expansion features")
