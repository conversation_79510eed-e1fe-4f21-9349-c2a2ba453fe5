"""
Robust security validator with comprehensive error handling for different environments.
"""

import json
import sys
import os
from typing import Dict, Any, Optional
import traceback


def safe_load_csv(csv_file: str) -> Optional[Dict[str, Dict[str, str]]]:
    """Safely load CSV with multiple fallback methods."""
    print(f"Attempting to load CSV: {csv_file}")
    
    # Check if file exists
    if not os.path.exists(csv_file):
        print(f"❌ CSV file not found: {csv_file}")
        print(f"Current working directory: {os.getcwd()}")
        print(f"Files in current directory: {os.listdir('.')}")
        return None
    
    try:
        # Try importing the csv_processor
        from csv_processor import csv_to_dict_simple
        result = csv_to_dict_simple(csv_file)
        print(f"✅ CSV loaded using csv_processor: {len(result) if result else 0} flows")
        return result
    except ImportError as e:
        print(f"⚠️ Could not import csv_processor: {e}")
    except Exception as e:
        print(f"⚠️ Error using csv_processor: {e}")
    
    # Fallback: Manual CSV processing
    try:
        import csv
        print("Attempting manual CSV processing...")
        
        flow_data = {}
        with open(csv_file, 'r', newline='', encoding='utf-8') as csvfile:
            csv_reader = csv.reader(csvfile)
            headers = next(csv_reader)
            clean_headers = [h.strip() for h in headers if h.strip()]
            
            flow_counter = 1
            for row in csv_reader:
                if row and len(row) > 0 and row[0].strip().startswith('Flow'):
                    row_dict = {}
                    for i, header in enumerate(clean_headers):
                        if i + 1 < len(row):
                            row_dict[header] = row[i + 1].strip()
                    
                    flow_key = f"flow_{flow_counter}"
                    flow_data[flow_key] = row_dict
                    flow_counter += 1
        
        print(f"✅ Manual CSV processing successful: {len(flow_data)} flows")
        return flow_data
        
    except Exception as e:
        print(f"❌ Manual CSV processing failed: {e}")
        traceback.print_exc()
        return None


def safe_load_json(file_path: str, description: str) -> Dict[str, Any]:
    """Safely load JSON file with error handling."""
    if not file_path:
        print(f"⚠️ No {description} file specified")
        return {}
    
    if not os.path.exists(file_path):
        print(f"⚠️ {description} file not found: {file_path}")
        return {}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"✅ {description} loaded successfully")
        return data
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in {description} file: {e}")
        return {}
    except Exception as e:
        print(f"❌ Error loading {description} file: {e}")
        return {}


def robust_validate(csv_file: str, unacceptable_file: str = None, guidance_file: str = None) -> Dict[str, Any]:
    """
    Robust validation function that handles various error conditions.
    """
    # Initialize safe results structure
    results = {
        "total_flows": 0,
        "flows_with_issues": 0,
        "critical_issues": 0,
        "high_risk_issues": 0,
        "medium_risk_issues": 0,
        "low_risk_issues": 0,
        "flow_results": {},
        "summary": [],
        "errors": [],
        "warnings": [],
        "environment_info": {
            "python_version": sys.version,
            "working_directory": os.getcwd(),
            "csv_file_exists": os.path.exists(csv_file) if csv_file else False,
            "unacceptable_file_exists": os.path.exists(unacceptable_file) if unacceptable_file else False,
            "guidance_file_exists": os.path.exists(guidance_file) if guidance_file else False
        }
    }
    
    try:
        # Step 1: Load CSV data
        flow_data = safe_load_csv(csv_file)
        if not flow_data:
            results["errors"].append("Failed to load CSV data")
            results["summary"].append("❌ CSV loading failed")
            return results
        
        results["total_flows"] = len(flow_data)
        
        # Step 2: Load configuration files (optional)
        unacceptable_values = safe_load_json(unacceptable_file, "unacceptable values")
        guidance = safe_load_json(guidance_file, "guidance")
        
        # Step 3: Process flows with basic validation
        for flow_id, flow_info in flow_data.items():
            try:
                flow_result = validate_single_flow_basic(flow_id, flow_info, unacceptable_values, guidance)
                results["flow_results"][flow_id] = flow_result
                
                # Count issues
                if flow_result.get("issues"):
                    results["flows_with_issues"] += 1
                    for issue in flow_result["issues"]:
                        risk_level = issue.get("risk_level", "low")
                        if risk_level == "critical":
                            results["critical_issues"] += 1
                        elif risk_level == "high":
                            results["high_risk_issues"] += 1
                        elif risk_level == "medium":
                            results["medium_risk_issues"] += 1
                        else:
                            results["low_risk_issues"] += 1
                            
            except Exception as e:
                error_msg = f"Error processing flow {flow_id}: {str(e)}"
                results["errors"].append(error_msg)
                print(f"⚠️ {error_msg}")
        
        # Step 4: Generate summary
        results["summary"] = generate_safe_summary(results)
        
    except Exception as e:
        error_msg = f"Unexpected error in validation: {str(e)}"
        results["errors"].append(error_msg)
        results["summary"].append(f"❌ Validation failed: {error_msg}")
        print(f"❌ {error_msg}")
        traceback.print_exc()
    
    return results


def validate_single_flow_basic(flow_id: str, flow_info: Dict[str, str], 
                              unacceptable_values: Dict, guidance: Dict) -> Dict[str, Any]:
    """Basic flow validation with error handling."""
    flow_result = {
        "flow_id": flow_id,
        "flow_data": flow_info,
        "issues": [],
        "guidance": [],
        "overall_risk": "low"
    }
    
    try:
        # Basic checks that don't require configuration files
        service = flow_info.get('Service', '').lower()
        action = flow_info.get('Action', '').lower()
        port = flow_info.get('Port', '')
        
        # Check for risky services
        risky_services = ['ssh', 'telnet', 'ftp', 'rdp']
        if service in risky_services:
            risk_level = "critical" if service in ['telnet', 'ftp'] else "high"
            flow_result["issues"].append({
                "field": "Service",
                "value": service,
                "risk_level": risk_level,
                "message": f"Service '{service}' is considered risky"
            })
            flow_result["overall_risk"] = risk_level
        
        # Check for risky ports
        risky_ports = ['23', '21', '22', '3389']
        if port in risky_ports:
            risk_level = "critical" if port in ['23', '21'] else "high"
            flow_result["issues"].append({
                "field": "Port",
                "value": port,
                "risk_level": risk_level,
                "message": f"Port '{port}' is considered risky"
            })
            if flow_result["overall_risk"] == "low":
                flow_result["overall_risk"] = risk_level
        
        # Check allow actions
        if action == 'allow':
            flow_result["issues"].append({
                "field": "Action",
                "value": action,
                "risk_level": "medium",
                "message": "Allow action requires review"
            })
            if flow_result["overall_risk"] == "low":
                flow_result["overall_risk"] = "medium"
        
        # Use configuration files if available
        if unacceptable_values:
            # Add more sophisticated checks here
            pass
            
    except Exception as e:
        flow_result["issues"].append({
            "field": "validation",
            "value": "error",
            "risk_level": "medium",
            "message": f"Error during validation: {str(e)}"
        })
    
    return flow_result


def generate_safe_summary(results: Dict[str, Any]) -> list:
    """Generate summary with safe key access."""
    summary = []
    
    try:
        total = results.get("total_flows", 0)
        issues = results.get("flows_with_issues", 0)
        
        summary.append(f"Analyzed {total} flows, {issues} flows have security issues")
        
        critical = results.get("critical_issues", 0)
        high = results.get("high_risk_issues", 0)
        medium = results.get("medium_risk_issues", 0)
        low = results.get("low_risk_issues", 0)
        
        if critical > 0:
            summary.append(f"🔴 CRITICAL: {critical} critical security issues found")
        if high > 0:
            summary.append(f"🟠 HIGH: {high} high-risk issues found")
        if medium > 0:
            summary.append(f"🟡 MEDIUM: {medium} medium-risk issues found")
        if low > 0:
            summary.append(f"🟢 LOW: {low} low-risk issues found")
        if issues == 0:
            summary.append("✅ No security issues detected")
            
        if results.get("errors"):
            summary.append(f"⚠️ {len(results['errors'])} errors encountered during validation")
            
    except Exception as e:
        summary.append(f"Error generating summary: {str(e)}")
    
    return summary


def main():
    """Main function for testing."""
    print("=== ROBUST VALIDATOR TEST ===")
    
    results = robust_validate(
        csv_file="Example.csv",
        unacceptable_file="unacceptable_values.json",
        guidance_file="security_guidance.json"
    )
    
    print(f"\n=== RESULTS ===")
    print(f"Environment Info:")
    for key, value in results.get("environment_info", {}).items():
        print(f"  {key}: {value}")
    
    print(f"\nValidation Results:")
    print(f"  Total flows: {results.get('total_flows', 'MISSING')}")
    print(f"  Flows with issues: {results.get('flows_with_issues', 'MISSING')}")
    print(f"  Critical issues: {results.get('critical_issues', 'MISSING')}")
    print(f"  High risk issues: {results.get('high_risk_issues', 'MISSING')}")
    
    if results.get("errors"):
        print(f"\nErrors:")
        for error in results["errors"]:
            print(f"  - {error}")
    
    print(f"\nSummary:")
    for line in results.get("summary", []):
        print(f"  {line}")
    
    # Test the key that was causing issues
    try:
        flows_with_issues = results["flows_with_issues"]
        print(f"\n✅ Successfully accessed 'flows_with_issues': {flows_with_issues}")
    except KeyError as e:
        print(f"\n❌ KeyError accessing 'flows_with_issues': {e}")
        print(f"Available keys: {list(results.keys())}")


if __name__ == "__main__":
    main()
