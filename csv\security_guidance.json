{"service_guidance": {"ssh": {"risk_level": "medium", "message": "SSH is a risky protocol when detected. Ensure proper authentication and monitoring.", "recommendations": ["Enable key-based authentication", "Monitor for brute force attempts", "Restrict source IPs"]}, "rdp": {"risk_level": "high", "message": "RDP is a high-risk protocol. Should be restricted and monitored.", "recommendations": ["Use VPN access", "Enable NLA", "Monitor for suspicious activity"]}, "telnet": {"risk_level": "critical", "message": "Telnet is unencrypted and should be blocked immediately.", "recommendations": ["Replace with SSH", "Block all telnet traffic", "Audit existing usage"]}, "ftp": {"risk_level": "high", "message": "FTP transmits credentials in plaintext and should be replaced.", "recommendations": ["Use SFTP or FTPS", "Block standard FTP", "Audit file transfers"]}, "https": {"risk_level": "low", "message": "HTTPS is generally secure but monitor for suspicious destinations.", "recommendations": ["Monitor certificate validity", "Check destination reputation"]}, "http": {"risk_level": "medium", "message": "HTTP is unencrypted and should be avoided for sensitive data.", "recommendations": ["Migrate to HTTPS", "Monitor for data leakage"]}}, "port_guidance": {"22": {"risk_level": "medium", "message": "SSH port - ensure proper security controls are in place.", "recommendations": ["Change default port", "Use key authentication", "Monitor access"]}, "23": {"risk_level": "critical", "message": "Telnet port - should be blocked immediately.", "recommendations": ["Block port 23", "Migrate to SSH", "Audit usage"]}, "3389": {"risk_level": "high", "message": "RDP port - high risk for attacks.", "recommendations": ["Use VPN", "Change default port", "Enable NLA"]}, "443": {"risk_level": "low", "message": "HTTPS port - generally secure.", "recommendations": ["Monitor certificate health", "Check for suspicious traffic"]}, "80": {"risk_level": "medium", "message": "HTTP port - unencrypted traffic.", "recommendations": ["Redirect to HTTPS", "Monitor for sensitive data"]}}, "action_guidance": {"Allow": {"risk_level": "varies", "message": "Traffic is being allowed - ensure this is intentional and secure.", "recommendations": ["Review allow rules regularly", "Monitor allowed traffic", "Implement least privilege"]}, "Deny": {"risk_level": "low", "message": "Traffic is being denied - good security posture.", "recommendations": ["Log denied attempts", "Review for legitimate traffic"]}, "Block": {"risk_level": "low", "message": "Traffic is being blocked - good security posture.", "recommendations": ["Log blocked attempts", "Review for legitimate traffic"]}}, "default_guidance": {"risk_level": "unknown", "message": "No security issues detected for this value.", "recommendations": ["Continue monitoring", "Review periodically"]}}