import json
import csv
from typing import Dict, List, Any, <PERSON><PERSON>


def find_data_table_start(csv_file_path: str) -> Tuple[int, List[str]]:
    """
    Automatically find where the actual data table starts by looking for Flow rows.

    Args:
        csv_file_path: Path to the CSV file

    Returns:
        Tuple of (header_row_index, clean_headers_list)
        Returns (-1, []) if no Flow rows found
    """
    try:
        with open(csv_file_path, 'r', newline='', encoding='utf-8') as csvfile:
            csv_reader = csv.reader(csvfile)

            for row_index, row in enumerate(csv_reader):
                if row and len(row) > 0:
                    # Check if this row starts with 'Flow' (case-insensitive)
                    first_col = str(row[0]).strip().lower() if row[0] else ""
                    if first_col.startswith('flow'):
                        # Found Flow row, previous row should be headers
                        if row_index > 0:
                            # Go back to get the header row
                            csvfile.seek(0)
                            csv_reader = csv.reader(csvfile)

                            # Skip to the row before the Flow row
                            header_row_index = max(0, row_index - 1)
                            for i in range(header_row_index + 1):
                                header_row = next(csv_reader)

                            # Clean headers and return (include all columns)
                            clean_headers = [header.strip() for header in header_row if header.strip()]
                            return header_row_index, clean_headers
                        else:
                            # Flow row is first row, use it as headers (include all columns)
                            clean_headers = [header.strip() for header in row if header.strip()]
                            return 0, clean_headers

            # No Flow rows found, try fallback detection
            print("⚠️  No Flow rows found, attempting fallback header detection...")

            # Look for common column headers
            csvfile.seek(0)
            csv_reader = csv.reader(csvfile)

            for row_index, row in enumerate(csv_reader):
                if row and len(row) > 1:
                    # Check if this looks like a header row
                    row_lower = [str(col).lower().strip() for col in row if col]
                    header_indicators = ['source', 'destination', 'ip', 'port', 'service', 'protocol', 'action', 'file', 'type']

                    if any(indicator in ' '.join(row_lower) for indicator in header_indicators):
                        clean_headers = [header.strip() for header in row if header.strip()]
                        return row_index, clean_headers

            return -1, []

    except Exception as e:
        print(f"Error finding data table start: {e}")
        return -1, []


def normalize_port_with_protocol(port_value: str) -> str:
    """
    Normalize port values while preserving protocol prefixes.

    Args:
        port_value: Port value that may include protocol prefix (e.g., "tcp\22", "udp\53")

    Returns:
        Normalized port value with consistent protocol prefix format
    """
    port_value = str(port_value).strip()

    # Handle protocol prefixes - normalize separators
    if '\\' in port_value:
        # Already has backslash separator
        parts = port_value.split('\\', 1)
        if len(parts) == 2:
            protocol = parts[0].lower().strip()
            port = parts[1].strip()
            return f"{protocol}\\{port}"
    elif '/' in port_value:
        # Convert forward slash to backslash for consistency
        parts = port_value.split('/', 1)
        if len(parts) == 2:
            protocol = parts[0].lower().strip()
            port = parts[1].strip()
            return f"{protocol}\\{port}"

    # No protocol prefix, return as-is
    return port_value


def expand_comma_separated_flows(base_flow: Dict[str, str], flow_id: str) -> List[Dict[str, str]]:
    """
    Expand a flow with comma-separated values into multiple individual flows.

    Args:
        base_flow: The original flow data
        flow_id: The base flow ID

    Returns:
        List of expanded flows
    """
    # Find fields with comma-separated values
    comma_fields = {}
    for field, value in base_flow.items():
        if value and ',' in str(value):
            # Split and clean the values, handling protocol prefixes
            split_values = [v.strip() for v in str(value).split(',') if v.strip()]

            # For port fields, preserve protocol prefixes (tcp\, udp\)
            if field.lower() == "port":
                split_values = [normalize_port_with_protocol(v) for v in split_values]
            if len(split_values) > 1:
                comma_fields[field] = split_values

    if not comma_fields:
        # No comma-separated fields, return original flow
        return [base_flow]

    # Generate all combinations of comma-separated values
    import itertools

    # Get field names and their value lists
    field_names = list(comma_fields.keys())
    value_lists = list(comma_fields.values())

    # Generate all combinations
    combinations = list(itertools.product(*value_lists))

    expanded_flows = []
    for combination in combinations:
        # Create a new flow based on the original
        new_flow = base_flow.copy()

        # Update with the specific combination values
        for field_name, new_value in zip(field_names, combination):
            new_flow[field_name] = new_value

        expanded_flows.append(new_flow)

    return expanded_flows


def csv_to_dict_simple(csv_file_path: str, expand_comma_separated: bool = True) -> Dict[str, Dict[str, str]]:
    """
    Simple function to read CSV and return flow rows as dictionaries.
    Uses automatic table detection to find data regardless of header content amount.
    Optionally expands comma-separated values into individual flows.

    Args:
        csv_file_path (str): Path to the CSV file
        expand_comma_separated (bool): Whether to expand comma-separated values into separate flows

    Returns:
        Dict[str, Dict[str, str]]: Dictionary with flow IDs as keys and flow data as values
    """
    flow_rows = {}

    try:
        # Auto-detect table structure
        header_row_index, clean_headers = find_data_table_start(csv_file_path)

        if not clean_headers:
            print(f"❌ Could not auto-detect table structure.")
            print(f"   • Ensure the CSV has rows starting with 'Flow' (any case)")
            print(f"   • Check that there are proper column headers before the Flow rows")
            print(f"   • Verify the file format is correct")
            return {}

        print(f"🔍 Auto-detected data table:")
        print(f"   Header row: {header_row_index + 1}")
        print(f"   First Flow row: {header_row_index + 2}")
        print(f"   Headers: {clean_headers}")

        # Read the file starting from the detected position
        with open(csv_file_path, 'r', newline='', encoding='utf-8') as csvfile:
            csv_reader = csv.reader(csvfile)

            # Skip to the first Flow row (header_row_index + 1)
            for _ in range(header_row_index + 1):
                next(csv_reader)

            # Process Flow rows
            flow_counter = 1
            for row in csv_reader:
                if row and len(row) > 0:
                    # More flexible flow detection - handle "flows", "Flow", "FLOW", etc.
                    first_col = str(row[0]).strip().lower() if row[0] else ""
                    if first_col.startswith('flow'):
                        # Create dictionary mapping headers to values
                        # Skip the first column (Flow identifier) and map remaining columns
                        row_dict = {}
                        has_data = False  # Track if this row has any actual data

                        # Map headers to row data, skipping the flow identifier column
                        for i, header in enumerate(clean_headers):
                            # Determine the correct row column index
                            # If first column is flow identifier, offset by 1
                            if str(row[0]).strip().lower().startswith('flow'):
                                row_column_index = i + 1
                            else:
                                row_column_index = i

                            if row_column_index < len(row):
                                # Get the raw cell value first
                                raw_value = row[row_column_index]
                                # Convert to string and strip, but preserve original for checking
                                cell_value = str(raw_value).strip() if raw_value is not None else ""

                                row_dict[header] = cell_value
                                # Check if this cell has actual data (not empty and not just whitespace)
                                if raw_value is not None and str(raw_value).strip():
                                    has_data = True
                            else:
                                # Column doesn't exist in this row, set empty
                                row_dict[header] = ""

                        # Only add rows that have at least some data
                        if has_data:
                            if expand_comma_separated:
                                # Expand comma-separated values into multiple flows
                                base_flow_id = f"flow_{flow_counter}"
                                expanded_flows = expand_comma_separated_flows(row_dict, base_flow_id)

                                if len(expanded_flows) > 1:
                                    print(f"   🔄 Expanding {len(expanded_flows)} flows from comma-separated values in {base_flow_id}")

                                # Add each expanded flow
                                for i, expanded_flow in enumerate(expanded_flows):
                                    if len(expanded_flows) == 1:
                                        # No expansion needed
                                        flow_key = base_flow_id
                                    else:
                                        # Multiple flows from expansion
                                        flow_key = f"{base_flow_id}_{chr(97 + i)}"  # flow_1_a, flow_1_b, etc.

                                    flow_rows[flow_key] = expanded_flow

                                flow_counter += 1
                            else:
                                # No expansion, add as-is
                                flow_key = f"flow_{flow_counter}"
                                flow_rows[flow_key] = row_dict
                                flow_counter += 1
                        else:
                            # Extract the flow identifier from the first column for better messaging
                            flow_identifier = str(row[0]).strip() if row[0] else f"Flow {flow_counter}"
                            print(f"   ⚠️  Skipping empty Flow row: {flow_identifier}")
                            flow_counter += 1  # Still increment counter to maintain numbering

    except FileNotFoundError:
        print(f"❌ Error: File '{csv_file_path}' not found.")
        return {}
    except Exception as e:
        print(f"❌ Error reading CSV file: {e}")
        return {}

    # Basic validation - check if we have data and required structure
    if flow_rows:
        print(f"\n✅ Successfully loaded {len(flow_rows)} flows")

        # Show basic structure validation
        sample_flow = next(iter(flow_rows.values()))
        print(f"\n📋 Detected CSV structure:")
        print(f"   Columns: {list(sample_flow.keys())}")

        # Check for common required columns
        has_source = any('source' in str(col).lower() for col in sample_flow.keys())
        has_dest = any('dest' in str(col).lower() for col in sample_flow.keys())
        has_service = any('service' in str(col).lower() or 'protocol' in str(col).lower() for col in sample_flow.keys())

        print(f"   Source column: {'✅' if has_source else '❌'}")
        print(f"   Destination column: {'✅' if has_dest else '❌'}")
        print(f"   Service/Protocol column: {'✅' if has_service else '❌'}")

    return flow_rows


def validate_csv_structure(flow_data: Dict[str, Dict[str, str]]) -> Tuple[Dict[str, Dict[str, str]], List[str]]:
    """
    Simple validation of CSV structure - just check basic format requirements.

    Args:
        flow_data: The loaded flow data

    Returns:
        Tuple of (flow_data, list_of_issues)
    """
    issues = []

    if not flow_data:
        issues.append("❌ No flow data found - check if CSV has data rows starting with 'Flow'")
        return {}, issues

    issues.append(f"✅ Found {len(flow_data)} flows")

    # Get sample flow to check basic structure
    sample_flow = next(iter(flow_data.values()))
    columns = list(sample_flow.keys())

    # Basic structure validation
    if len(columns) >= 3:
        issues.append(f"✅ CSV has {len(columns)} columns")
    else:
        issues.append(f"⚠️  CSV has only {len(columns)} columns - may be missing data")

    # Check if flows have data
    has_data = any(value.strip() for value in sample_flow.values())
    if has_data:
        issues.append(f"✅ Flow rows contain data")
    else:
        issues.append(f"❌ Flow rows appear to be empty")

    # Just return the data as-is without any formatting changes
    return flow_data, issues


class SecurityValidator:
    def __init__(self, unacceptable_values_file: str, guidance_file: str):
        """
        Initialize the security validator with reference files.

        Args:
            unacceptable_values_file: Path to JSON file with unacceptable values
            guidance_file: Path to JSON file with security guidance
        """
        self.unacceptable_values = self._load_json(unacceptable_values_file)
        self.guidance = self._load_json(guidance_file)

    def validate_csv_file(self, csv_file_path: str) -> Dict[str, Any]:
        """
        Complete CSV validation and security analysis in one function.

        Args:
            csv_file_path: Path to the CSV file to validate

        Returns:
            Dictionary containing complete validation and security results
        """
        print(f"🔍 CSV Security Validation")
        print("=" * 60)
        print(f"📁 File: {csv_file_path}")
        print(f"🤖 Mode: Automatic table detection")

        # Step 1: Load and validate CSV structure
        print(f"\n📥 Loading CSV data...")
        flow_data = csv_to_dict_simple(csv_file_path, expand_comma_separated=True)

        if not flow_data:
            return {
                "success": False,
                "error": "Failed to load CSV data",
                "total_flows": 0,
                "flows_with_issues": 0,
                "critical_issues": 0,
                "high_risk_issues": 0,
                "medium_risk_issues": 0,
                "low_risk_issues": 0,
                "flow_results": {},
                "summary": ["❌ CSV validation failed"],
                "errors": ["Could not load or parse CSV file"]
            }

        # Step 2: Basic structure validation
        validated_data, validation_issues = validate_csv_structure(flow_data)

        print(f"\n✅ Successfully loaded and validated {len(validated_data)} flows")

        # Show structure information
        if validated_data:
            sample_flow = next(iter(validated_data.values()))
            print(f"\n📋 Detected CSV structure:")
            print(f"   Columns: {list(sample_flow.keys())}")

            # Check for key columns
            has_file_type = any("file" in str(col).lower() and "type" in str(col).lower() for col in sample_flow.keys())
            has_source_ip = any("source" in str(col).lower() and "ip" in str(col).lower() for col in sample_flow.keys())
            has_service = any("service" in str(col).lower() or "protocol" in str(col).lower() for col in sample_flow.keys())

            print(f"   File Type column: {'✅' if has_file_type else '❌'}")
            print(f"   Source IP column: {'✅' if has_source_ip else '❌'}")
            print(f"   Service column: {'✅' if has_service else '❌'}")

            if not has_file_type:
                print(f"   ⚠️  No File Type column - file type validation will be skipped")

        # Step 3: Security validation
        print(f"\n🔍 Running security validation...")
        security_results = self.validate_flow_data(validated_data)

        # Step 4: Generate comprehensive output
        self._display_security_results(security_results, validated_data)

        # Step 5: Generate report
        report_file = "security_validation_report.md"
        print(f"\n📄 Generating comprehensive report...")
        generate_markdown_report(security_results, validated_data, csv_file_path, report_file)
        print(f"✅ Detailed report saved: {report_file}")

        # Step 6: Summary and recommendations
        self._display_summary_and_recommendations(security_results)

        # Add success flag and CSV validation info
        security_results["success"] = True
        security_results["csv_validation"] = {
            "structure_issues": validation_issues,
            "columns_detected": list(sample_flow.keys()) if validated_data else [],
            "auto_detection_successful": True
        }

        return security_results

    def _display_security_results(self, results: Dict[str, Any], flow_data: Dict[str, Dict[str, str]]):
        """Display security validation results in a formatted way."""
        # Get sample flow for structure analysis
        sample_flow = next(iter(flow_data.values())) if flow_data else {}
        has_file_type = any("file" in str(col).lower() and "type" in str(col).lower() for col in sample_flow.keys())

        # Initialize detection lists for all cases
        critical_detections = []
        high_risk_detections = []

        # Display main results
        print(f"\n📊 Security Validation Results:")
        print(f"   • Total flows analyzed: {results['total_flows']}")
        print(f"   • Flows with security issues: {results['flows_with_issues']}")
        print(f"   • Critical risk issues: {results['critical_issues']} 🔴")
        print(f"   • High risk issues: {results['high_risk_issues']} 🟠")
        print(f"   • Medium risk issues: {results['medium_risk_issues']} 🟡")
        print(f"   • Low risk issues: {results['low_risk_issues']} 🟢")

        # File type analysis (if available)
        if has_file_type:
            print(f"\n📁 File Type Security Analysis:")
            print("-" * 50)

            file_type_summary = {}

            for flow_id, flow_result in results["flow_results"].items():
                flow_data_item = flow_result.get("flow_data", {})
                file_type_field = flow_data_item.get("File Type", "")
                source_ip = flow_data_item.get("Source IP", "unknown")
                dest_ip = flow_data_item.get("Destination IP", "unknown")
                service = flow_data_item.get("Service", "unknown")
                action = flow_data_item.get("Action", "unknown")

                if file_type_field:
                    # Handle comma-separated file types
                    file_types = [ft.strip() for ft in file_type_field.split(',')]
                    display_types = file_type_field if ',' in file_type_field else file_types[0]

                    # Check for security issues in this flow
                    flow_issues = flow_result.get("issues", [])
                    file_type_issues = [issue for issue in flow_issues if issue.get("field") == "File Type"]

                    if file_type_issues:
                        # Track file types for summary
                        for file_type in file_types:
                            file_type_summary[file_type] = file_type_summary.get(file_type, 0) + 1

                        # Process each issue for this flow
                        for issue in file_type_issues:
                            risk_level = str(issue.get("risk_level", "unknown")).lower()
                            detection = {
                                "flow_id": flow_id,
                                "display_types": display_types,
                                "source_ip": source_ip,
                                "dest_ip": dest_ip,
                                "service": service,
                                "action": action,
                                "message": issue.get("message", "")
                            }

                            if risk_level == "critical":
                                critical_detections.append(detection)
                            elif risk_level == "high":
                                high_risk_detections.append(detection)
                    else:
                        print(f"   ✅ {flow_id}: {display_types} - No security issues")

            # Show file type distribution
            if file_type_summary:
                print(f"\n📊 File Type Distribution:")
                sorted_types = sorted(file_type_summary.items(), key=lambda x: x[1], reverse=True)
                for file_type, count in sorted_types:
                    print(f"   • {file_type}: {count} occurrences")

            # Show critical detections
            if critical_detections:
                print(f"\n🔴 CRITICAL File Type Detections:")
                print("-" * 40)
                for detection in critical_detections:
                    print(f"   🔴 {detection['flow_id']}: {detection['display_types']}")
                    print(f"      {detection['source_ip']} → {detection['dest_ip']} via {detection['service']} ({detection['action']})")
                    print(f"      Risk: CRITICAL - {detection['message']}")
                    print()

            # Show high risk detections
            if high_risk_detections:
                print(f"\n🟠 HIGH RISK File Type Detections:")
                print("-" * 40)
                for detection in high_risk_detections:
                    print(f"   🟠 {detection['flow_id']}: {detection['display_types']}")
                    print(f"      {detection['source_ip']} → {detection['dest_ip']} via {detection['service']} ({detection['action']})")
                    print(f"      Risk: HIGH - {detection['message']}")
                    print()

            if not critical_detections and not high_risk_detections:
                print(f"\n✅ No critical or high-risk file types detected")

        # Protocol/Service analysis
        print(f"\n🌐 Protocol/Service Analysis:")
        print("-" * 35)

        service_summary = {}
        for flow_result in results["flow_results"].values():
            flow_data_item = flow_result.get("flow_data", {})
            service = str(flow_data_item.get("Service", "unknown")).lower()

            if service not in service_summary:
                service_summary[service] = {"count": 0, "max_risk": "low"}

            service_summary[service]["count"] += 1

            # Determine max risk for this service
            flow_issues = flow_result.get("issues", [])
            service_issues = [issue for issue in flow_issues if issue.get("field") == "Service"]

            for issue in service_issues:
                risk_level = str(issue.get("risk_level", "low")).lower()
                current_max = service_summary[service]["max_risk"]

                # Risk hierarchy: critical > high > medium > low
                risk_hierarchy = {"critical": 4, "high": 3, "medium": 2, "low": 1}
                if risk_hierarchy.get(risk_level, 1) > risk_hierarchy.get(current_max, 1):
                    service_summary[service]["max_risk"] = risk_level

        # Display service analysis
        for service, info in service_summary.items():
            count = info["count"]
            max_risk = info["max_risk"]

            if max_risk == "critical":
                emoji = "🔴"
            elif max_risk == "high":
                emoji = "🟠"
            elif max_risk == "medium":
                emoji = "🟡"
            else:
                emoji = "🟢"

            print(f"   {emoji} {service}: {count} flows, max risk: {max_risk}")

    def _display_summary_and_recommendations(self, results: Dict[str, Any]):
        """Display summary and recommendations."""
        total_issues = results.get('critical_issues', 0) + results.get('high_risk_issues', 0)

        print(f"\n🎯 Summary and Recommendations:")
        if total_issues > 0:
            print(f"   ⚠️  {total_issues} critical/high-risk issues found")
            print(f"   📋 Review the detailed report: security_validation_report.md")
            print(f"   🔍 Focus on critical (🔴) and high-risk (🟠) items first")
        else:
            print(f"   ✅ No critical or high-risk security issues detected")
            print(f"   📋 Review medium/low risk items in: security_validation_report.md")

        print(f"\n✅ Validation completed successfully!")

        print(f"\n💡 Features Used:")
        print(f"   🤖 Automatic table detection (finds data regardless of header count)")
        print(f"   🔍 Flexible flow detection (handles 'flows', 'Flow', 'FLOW')")
        print(f"   📋 Basic structure validation")
        print(f"   📊 Security analysis and reporting")

    def _load_json(self, file_path: str) -> Dict:
        """Load JSON file and return as dictionary."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Warning: File '{file_path}' not found.")
            return {}
        except json.JSONDecodeError as e:
            print(f"Error parsing JSON file '{file_path}': {e}")
            return {}
    
    def validate_flow_data(self, flow_data: Dict[str, Dict[str, str]]) -> Dict[str, Any]:
        """
        Validate flow data against unacceptable values and provide guidance.

        Args:
            flow_data: Dictionary of flow data from CSV

        Returns:
            Dictionary containing validation results and security guidance
        """
        # Initialize results with safe defaults
        validation_results = {
            "total_flows": 0,
            "flows_with_issues": 0,
            "critical_issues": 0,
            "high_risk_issues": 0,
            "medium_risk_issues": 0,
            "low_risk_issues": 0,
            "flow_results": {},
            "summary": [],
            "errors": []
        }

        # Validate input
        if not flow_data:
            validation_results["errors"].append("No flow data provided")
            validation_results["summary"].append("❌ No flow data to validate")
            return validation_results

        if not isinstance(flow_data, dict):
            validation_results["errors"].append(f"Expected dict, got {type(flow_data)}")
            validation_results["summary"].append("❌ Invalid flow data format")
            return validation_results

        # Set total flows after validation
        validation_results["total_flows"] = len(flow_data)

        try:
            for flow_id, flow_info in flow_data.items():
                try:
                    # Debug: Check for unusual flow IDs
                    if not isinstance(flow_id, str) or not flow_id.startswith('flow_'):
                        print(f"⚠️  Warning: Unusual flow ID detected: '{flow_id}' (type: {type(flow_id)})")
                        print(f"   Flow data: {flow_info}")
                        continue  # Skip this entry

                    # Debug: Check flow_info structure
                    if not isinstance(flow_info, dict):
                        print(f"⚠️  Warning: Flow {flow_id} has invalid data type: {type(flow_info)}")
                        print(f"   Data: {flow_info}")
                        continue  # Skip this entry

                    flow_result = self._validate_single_flow(flow_id, flow_info)
                    validation_results["flow_results"][flow_id] = flow_result

                    # Count issues by severity
                    if flow_result.get("issues"):
                        validation_results["flows_with_issues"] += 1
                        for issue in flow_result["issues"]:
                            risk_level = issue.get("risk_level", "unknown")
                            if risk_level == "critical":
                                validation_results["critical_issues"] += 1
                            elif risk_level == "high":
                                validation_results["high_risk_issues"] += 1
                            elif risk_level == "medium":
                                validation_results["medium_risk_issues"] += 1
                            elif risk_level == "low":
                                validation_results["low_risk_issues"] += 1

                except Exception as e:
                    error_msg = f"Error validating flow {flow_id}: {str(e)}"
                    validation_results["errors"].append(error_msg)
                    print(f"Warning: {error_msg}")
                    print(f"   Flow ID type: {type(flow_id)}")
                    print(f"   Flow data type: {type(flow_info)}")
                    print(f"   Flow data: {flow_info}")
                    # Continue processing other flows instead of failing completely

            # Generate summary
            validation_results["summary"] = self._generate_summary(validation_results)

        except Exception as e:
            error_msg = f"Error during validation: {str(e)}"
            validation_results["errors"].append(error_msg)
            validation_results["summary"].append(f"❌ Validation failed: {str(e)}")
            print(f"Error: {error_msg}")

        return validation_results
    
    def _validate_single_flow(self, flow_id: str, flow_info: Dict[str, str]) -> Dict[str, Any]:
        """Validate a single flow and return issues and guidance."""
        flow_result = {
            "flow_id": flow_id,
            "flow_data": flow_info,
            "issues": [],
            "guidance": [],
            "overall_risk": "low"
        }
        
        # Check each field in the flow
        for field, value in flow_info.items():
            if not value:  # Skip empty values
                continue

            # Check against unacceptable values
            field_issues = self._check_unacceptable_values(field, value)
            flow_result["issues"].extend(field_issues)

            # Get security guidance - special handling for comma-separated file types
            if str(field).lower() == "file type" and "," in str(value):
                # Handle comma-separated file types for guidance
                file_types = [str(ft).strip().lower() for ft in str(value).split(",") if str(ft).strip()]
                for file_type in file_types:
                    field_guidance = self._get_security_guidance(field, file_type)
                    if field_guidance:
                        # Update the guidance to show it's from a comma-separated list
                        field_guidance["value"] = f"{file_type} (from '{value}')"
                        flow_result["guidance"].append(field_guidance)
            else:
                # Standard single value guidance
                field_guidance = self._get_security_guidance(field, value)
                if field_guidance:
                    flow_result["guidance"].append(field_guidance)
        
        # Determine overall risk level
        flow_result["overall_risk"] = self._determine_overall_risk(flow_result["issues"])
        
        return flow_result
    
    def _check_unacceptable_values(self, field: str, value: str) -> List[Dict[str, Any]]:
        """Check if a field value is in the unacceptable values list."""
        issues = []

        if field not in self.unacceptable_values:
            return issues

        field_rules = self.unacceptable_values[field]

        # Special handling for File Type field - support comma-separated values
        if str(field).lower() == "file type" and "," in str(value):
            # Split comma-separated file types and check each one
            file_types = [str(ft).strip().lower() for ft in str(value).split(",") if str(ft).strip()]

            for file_type in file_types:
                for category, values in field_rules.items():
                    if isinstance(values, list) and file_type in [str(v).lower() for v in values]:
                        issues.append({
                            "field": field,
                            "value": file_type,
                            "category": category,
                            "risk_level": self._map_category_to_risk(category),
                            "message": f"{field} '{file_type}' (from '{value}') is classified as {category}"
                        })
        else:
            # Standard single value checking
            for category, values in field_rules.items():
                if isinstance(values, list) and str(value).lower() in [str(v).lower() for v in values]:
                    issues.append({
                        "field": field,
                        "value": value,
                        "category": category,
                        "risk_level": self._map_category_to_risk(category),
                        "message": f"{field} '{value}' is classified as {category}"
                    })

        return issues
    
    def _get_security_guidance(self, field: str, value: str) -> Dict[str, Any]:
        """Get security guidance for a specific field value."""
        guidance = None
        
        # Check service guidance
        if str(field).lower() == "service" and "service_guidance" in self.guidance:
            guidance = self.guidance["service_guidance"].get(str(value).lower())

        # Check port guidance
        elif str(field).lower() == "port" and "port_guidance" in self.guidance:
            guidance = self.guidance["port_guidance"].get(str(value))

        # Check action guidance
        elif str(field).lower() == "action" and "action_guidance" in self.guidance:
            guidance = self.guidance["action_guidance"].get(str(value))

        # Check file type guidance
        elif str(field).lower() == "file type" and "file_type_guidance" in self.guidance:
            guidance = self.guidance["file_type_guidance"].get(str(value).lower())

        # Default guidance if no specific guidance found
        if not guidance and "default_guidance" in self.guidance:
            guidance = self.guidance["default_guidance"]
        
        if guidance:
            return {
                "field": field,
                "value": value,
                "risk_level": guidance.get("risk_level", "unknown"),
                "message": guidance.get("message", "No guidance available"),
                "recommendations": guidance.get("recommendations", [])
            }
        
        return None
    
    def _map_category_to_risk(self, category: str) -> str:
        """Map unacceptable value categories to risk levels."""
        risk_mapping = {
            "blocked": "critical",
            "risky": "high", 
            "deprecated": "medium",
            "concerning": "medium",
            "suspicious": "high",
            "acceptable": "low"
        }
        return risk_mapping.get(str(category).lower(), "medium")
    
    def _determine_overall_risk(self, issues: List[Dict[str, Any]]) -> str:
        """Determine overall risk level based on all issues."""
        if not issues:
            return "low"
        
        risk_levels = [issue.get("risk_level", "low") for issue in issues]
        
        if "critical" in risk_levels:
            return "critical"
        elif "high" in risk_levels:
            return "high"
        elif "medium" in risk_levels:
            return "medium"
        else:
            return "low"
    
    def _generate_summary(self, results: Dict[str, Any]) -> List[str]:
        """Generate a summary of validation results."""
        summary = []
        
        total = results["total_flows"]
        issues = results["flows_with_issues"]
        
        summary.append(f"Analyzed {total} flows, {issues} flows have security issues")
        
        if results["critical_issues"] > 0:
            summary.append(f"🔴 CRITICAL: {results['critical_issues']} critical security issues found")
        
        if results["high_risk_issues"] > 0:
            summary.append(f"🟠 HIGH: {results['high_risk_issues']} high-risk issues found")
        
        if results["medium_risk_issues"] > 0:
            summary.append(f"🟡 MEDIUM: {results['medium_risk_issues']} medium-risk issues found")
        
        if results["low_risk_issues"] > 0:
            summary.append(f"🟢 LOW: {results['low_risk_issues']} low-risk issues found")
        
        if issues == 0:
            summary.append("✅ No security issues detected")
        
        return summary


def analyze_flow_expansion(flow_data: Dict[str, Dict[str, str]]) -> Dict[str, Any]:
    """
    Analyze flow expansion patterns to understand comma-separated value processing.

    Args:
        flow_data: Dictionary of flow data

    Returns:
        Dictionary with expansion analysis
    """
    # Handle case where flow_data might not be a dictionary
    if not isinstance(flow_data, dict):
        return {
            "total_flows": 0,
            "original_flows": 0,
            "expanded_flows": 0,
            "expansion_groups": {},
            "expansion_summary": []
        }

    expansion_info = {
        "total_flows": len(flow_data),
        "original_flows": 0,
        "expanded_flows": 0,
        "expansion_groups": {},
        "expansion_summary": []
    }

    # Group flows by their base ID (before _a, _b suffixes)
    flow_groups = {}
    for flow_id in flow_data.keys():
        if '_' in flow_id and flow_id.split('_')[-1].isalpha() and len(flow_id.split('_')[-1]) == 1:
            # This is an expanded flow (e.g., flow_2_a)
            base_id = '_'.join(flow_id.split('_')[:-1])  # flow_2
            if base_id not in flow_groups:
                flow_groups[base_id] = []
            flow_groups[base_id].append(flow_id)
        else:
            # This is an original flow
            if flow_id not in flow_groups:
                flow_groups[flow_id] = [flow_id]

    # Analyze each group
    for base_id, flow_ids in flow_groups.items():
        if len(flow_ids) == 1:
            expansion_info["original_flows"] += 1
        else:
            expansion_info["expanded_flows"] += len(flow_ids)
            expansion_info["expansion_groups"][base_id] = {
                "count": len(flow_ids),
                "flows": flow_ids
            }
            expansion_info["expansion_summary"].append(
                f"{base_id} expanded into {len(flow_ids)} flows: {', '.join(flow_ids)}"
            )

    return expansion_info


def generate_markdown_report(results: Dict[str, Any], flow_data: Dict[str, Dict[str, str]] = None, csv_file: str = "Example.csv",
                           output_file: str = "security_validation_report.md") -> str:
    """
    Generate a comprehensive markdown report from validation results.

    Args:
        results: Validation results from validate_flow_data()
        flow_data: Original flow data to analyze expansion patterns
        csv_file: Name of the CSV file that was validated
        output_file: Output markdown file path

    Returns:
        str: Path to generated report file
    """
    from datetime import datetime

    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    total_flows = results.get('total_flows', 0)
    flows_with_issues = results.get('flows_with_issues', 0)
    critical_issues = results.get('critical_issues', 0)
    high_risk_issues = results.get('high_risk_issues', 0)
    medium_risk_issues = results.get('medium_risk_issues', 0)
    low_risk_issues = results.get('low_risk_issues', 0)

    # Determine overall status
    if critical_issues > 0:
        status = "🔴 **CRITICAL**"
        status_desc = "Immediate action required"
    elif high_risk_issues > 0:
        status = "🟠 **HIGH RISK**"
        status_desc = "Significant security concerns identified"
    elif flows_with_issues > 0:
        status = "🟡 **MEDIUM RISK**"
        status_desc = "Some security issues require attention"
    else:
        status = "🟢 **SECURE**"
        status_desc = "No significant security issues detected"

    compliance_rate = ((total_flows - flows_with_issues) / total_flows * 100) if total_flows > 0 else 0

    # Analyze expansion patterns
    if flow_data is not None:
        expansion_info = analyze_flow_expansion(flow_data)
    else:
        # Fallback if flow_data is not provided
        expansion_info = {
            "total_flows": total_flows,
            "original_flows": total_flows,
            "expanded_flows": 0,
            "expansion_groups": {},
            "expansion_summary": []
        }

    # Build markdown report
    report_content = f"""# 🔒 CSV Security Validation Report

**Generated:** {timestamp}
**CSV File:** `{csv_file}`
**Validation Engine:** CSV Security Validator v1.0

---

## 📊 Executive Summary

### Overall Security Status: {status}
*{status_desc}*

### Key Metrics
| Metric | Value | Status |
|--------|-------|--------|
| **Total Flows Analyzed** | {total_flows} | ℹ️ |
| **Flows with Issues** | {flows_with_issues} | {'🔴' if flows_with_issues > 0 else '🟢'} |
| **Compliance Rate** | {compliance_rate:.1f}% | {'🟢' if compliance_rate >= 90 else '🟡' if compliance_rate >= 70 else '🔴'} |
| **Critical Issues** | {critical_issues} | {'🔴' if critical_issues > 0 else '🟢'} |
| **High Risk Issues** | {high_risk_issues} | {'🟠' if high_risk_issues > 0 else '🟢'} |
| **Medium Risk Issues** | {medium_risk_issues} | {'🟡' if medium_risk_issues > 0 else '🟢'} |
| **Low Risk Issues** | {low_risk_issues} | {'🟢' if low_risk_issues > 0 else '⚪'} |

## 🔄 Flow Expansion Analysis

| Metric | Value | Details |
|--------|-------|---------|
| **Original CSV Rows** | {len([g for g in expansion_info['expansion_groups'].values() if g['count'] == 1]) + len(expansion_info['expansion_groups'])} | Rows in source CSV |
| **Total Flows Analyzed** | {expansion_info['total_flows']} | After comma-separated expansion |
| **Flows from Expansion** | {expansion_info['expanded_flows']} | Created from comma-separated values |
| **Expansion Groups** | {len(expansion_info['expansion_groups'])} | CSV rows that were expanded |

{f"### 🔄 Expansion Details" if expansion_info['expansion_groups'] else ""}
{chr(10).join([f"- **{base_id}**: {info['count']} flows ({', '.join(info['flows'])})" for base_id, info in expansion_info['expansion_groups'].items()]) if expansion_info['expansion_groups'] else "No comma-separated values were expanded."}

## 🎯 Risk Level Breakdown

```
Critical: {critical_issues:>3} issues  {'█' * min(critical_issues, 20)}
High:     {high_risk_issues:>3} issues  {'█' * min(high_risk_issues, 20)}
Medium:   {medium_risk_issues:>3} issues  {'█' * min(medium_risk_issues, 20)}
Low:      {low_risk_issues:>3} issues  {'█' * min(low_risk_issues, 20)}
```

## 📋 Summary
"""

    # Add summary lines
    summary_lines = results.get('summary', [])
    for line in summary_lines:
        if "CRITICAL" in line:
            report_content += f"- 🔴 {line}\n"
        elif "HIGH" in line:
            report_content += f"- 🟠 {line}\n"
        elif "MEDIUM" in line:
            report_content += f"- 🟡 {line}\n"
        elif "LOW" in line:
            report_content += f"- 🟢 {line}\n"
        else:
            report_content += f"- ℹ️ {line}\n"

    # Add detailed findings
    flow_results = results.get('flow_results', {})
    if flow_results:
        report_content += "\n## 🔍 Detailed Findings\n\n"

        # Group by risk level
        risk_groups = {'critical': [], 'high': [], 'medium': [], 'low': []}
        for flow_id, flow_result in flow_results.items():
            if flow_result.get('issues'):
                risk_level = flow_result.get('overall_risk', 'low')
                risk_groups[risk_level].append((flow_id, flow_result))

        for risk_level, flows in risk_groups.items():
            if flows:
                risk_emoji = {'critical': '🔴', 'high': '🟠', 'medium': '🟡', 'low': '🟢'}
                report_content += f"### {risk_emoji[risk_level]} {risk_level.title()} Risk Issues\n\n"

                for flow_id, flow_result in flows:
                    flow_data_item = flow_result.get('flow_data', {})
                    issues = flow_result.get('issues', [])

                    # Check if this is an expanded flow
                    is_expanded = '_' in flow_id and flow_id.split('_')[-1].isalpha() and len(flow_id.split('_')[-1]) == 1
                    base_id = '_'.join(flow_id.split('_')[:-1]) if is_expanded else flow_id

                    expansion_note = f" *(expanded from {base_id})*" if is_expanded else ""

                    report_content += f"#### {flow_id}{expansion_note}\n\n"

                    if is_expanded:
                        # Show which fields were expanded
                        expanded_fields = []
                        for field, value in flow_data_item.items():
                            if ',' not in str(value):  # This field was likely part of expansion
                                # Check if other flows in the same group have different values for this field
                                group_flows = expansion_info['expansion_groups'].get(base_id, {}).get('flows', [])
                                if len(group_flows) > 1:
                                    other_values = []
                                    for other_flow_id in group_flows:
                                        if other_flow_id != flow_id and other_flow_id in results['flow_results']:
                                            other_flow_data = results['flow_results'][other_flow_id].get('flow_data', {})
                                            other_value = other_flow_data.get(field, '')
                                            if other_value != value and other_value not in other_values:
                                                other_values.append(other_value)
                                    if other_values:
                                        expanded_fields.append(field)

                        if expanded_fields:
                            report_content += f"**Expansion Context:** This flow was created from comma-separated values in: {', '.join(expanded_fields)}\n\n"

                    report_content += "**Flow Details:**\n"
                    report_content += "| Field | Value |\n|-------|-------|\n"
                    for field, value in flow_data_item.items():
                        report_content += f"| {field} | `{value}` |\n"

                    report_content += "\n**Security Issues:**\n"
                    for issue in issues:
                        field = issue.get('field', 'Unknown')
                        message = issue.get('message', 'No message')
                        risk = issue.get('risk_level', 'unknown')
                        risk_emoji_issue = {'critical': '🔴', 'high': '🟠', 'medium': '🟡', 'low': '🟢'}.get(risk, '⚪')
                        report_content += f"- {risk_emoji_issue} **{field}**: {message}\n"

                    report_content += "\n"

    # Add errors if any
    errors = results.get('errors', [])
    if errors:
        report_content += "## ⚠️ Validation Errors\n\n"
        for error in errors:
            report_content += f"- {error}\n"

    # Add footer
    report_content += f"\n---\n*Report generated by CSV Security Validator - {timestamp}*\n"

    # Write to file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(report_content)

    return output_file


# Backward compatibility wrapper for old function signature
def generate_markdown_report_legacy(results: Dict[str, Any], csv_file: str = "Example.csv",
                                   output_file: str = "security_validation_report.md") -> str:
    """
    Legacy wrapper for generate_markdown_report with old signature.
    Maintains backward compatibility for existing code.
    """
    return generate_markdown_report(results, None, csv_file, output_file)


def export_csv_data_to_yaml(flow_data: Dict[str, Dict[str, str]],
                           output_file: str = "csv_data_export.yaml") -> str:
    """
    Export raw CSV data to a YAML file without validation results.

    Args:
        flow_data: Dictionary of flow data from csv_to_dict_simple()
        output_file: Output YAML file path

    Returns:
        str: Path to generated YAML file
    """
    import yaml
    from datetime import datetime

    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Build YAML structure for raw CSV data
    yaml_data = {
        'metadata': {
            'exported': timestamp,
            'total_flows': len(flow_data),
            'export_type': 'CSV Data Export'
        },
        'flows': {}
    }

    # Add flow data
    for flow_id, flow_details in flow_data.items():
        # Start with standard fields
        flow_entry = {
            'source_ip': flow_details.get('Source IP', ''),
            'destination_ip': flow_details.get('Destination IP', ''),
            'port': flow_details.get('Port', ''),
            'service': flow_details.get('Service', ''),
            'action': flow_details.get('Action', '')
        }

        # Add any additional fields from the CSV
        for k, v in flow_details.items():
            if k not in ['Source IP', 'Destination IP', 'Port', 'Service', 'Action']:
                field_name = str(k).lower().replace(' ', '_')
                # Ensure we're only adding string values
                if isinstance(v, (str, int, float)):
                    flow_entry[field_name] = str(v)

        yaml_data['flows'][flow_id] = flow_entry

    # Write to YAML file
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            yaml.dump(yaml_data, f, default_flow_style=False, allow_unicode=True,
                     sort_keys=False, indent=2)
        return output_file

    except ImportError:
        # Fallback if PyYAML is not installed
        yaml_content = f"""# CSV Data Export
metadata:
  exported: "{timestamp}"
  total_flows: {len(flow_data)}
  export_type: "CSV Data Export"

flows:
"""

        for flow_id, flow_details in flow_data.items():
            yaml_content += f"  {flow_id}:\n"
            yaml_content += f"    source_ip: \"{flow_details.get('Source IP', '')}\"\n"
            yaml_content += f"    destination_ip: \"{flow_details.get('Destination IP', '')}\"\n"
            yaml_content += f"    port: \"{flow_details.get('Port', '')}\"\n"
            yaml_content += f"    service: \"{flow_details.get('Service', '')}\"\n"
            yaml_content += f"    action: \"{flow_details.get('Action', '')}\"\n"

            # Add additional fields safely
            for k, v in flow_details.items():
                if k not in ['Source IP', 'Destination IP', 'Port', 'Service', 'Action']:
                    field_name = str(k).lower().replace(' ', '_')
                    # Ensure we're only adding string/numeric values
                    if isinstance(v, (str, int, float)):
                        yaml_content += f"    {field_name}: \"{str(v)}\"\n"

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(yaml_content)

        return output_file


def export_to_yaml(results: Dict[str, Any], csv_file: str = "Example.csv",
                   output_file: str = "security_validation_results.yaml") -> str:
    """
    Export validation results and CSV data to a YAML file.

    Args:
        results: Validation results from validate_flow_data()
        csv_file: Name of the CSV file that was validated
        output_file: Output YAML file path

    Returns:
        str: Path to generated YAML file
    """
    import yaml
    from datetime import datetime

    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Build YAML structure
    yaml_data = {
        'metadata': {
            'generated': timestamp,
            'csv_file': csv_file,
            'validation_engine': 'CSV Security Validator v1.0',
            'total_flows': results.get('total_flows', 0),
            'flows_with_issues': results.get('flows_with_issues', 0)
        },
        'summary': {
            'critical_issues': results.get('critical_issues', 0),
            'high_risk_issues': results.get('high_risk_issues', 0),
            'medium_risk_issues': results.get('medium_risk_issues', 0),
            'low_risk_issues': results.get('low_risk_issues', 0),
            'compliance_rate': round(
                ((results.get('total_flows', 0) - results.get('flows_with_issues', 0)) /
                 results.get('total_flows', 1) * 100), 2
            ) if results.get('total_flows', 0) > 0 else 0
        },
        'validation_summary': results.get('summary', []),
        'flows': {}
    }

    # Add flow details
    flow_results = results.get('flow_results', {})
    for flow_id, flow_result in flow_results.items():
        flow_data = flow_result.get('flow_data', {})
        issues = flow_result.get('issues', [])
        guidance = flow_result.get('guidance', [])

        yaml_data['flows'][flow_id] = {
            'flow_details': {
                'source_ip': flow_data.get('Source IP', ''),
                'destination_ip': flow_data.get('Destination IP', ''),
                'port': flow_data.get('Port', ''),
                'service': flow_data.get('Service', ''),
                'action': flow_data.get('Action', ''),
                # Add any additional fields from the CSV
                **{str(k).lower().replace(' ', '_'): v for k, v in flow_data.items()
                   if k not in ['Source IP', 'Destination IP', 'Port', 'Service', 'Action']}
            },
            'security_assessment': {
                'overall_risk': flow_result.get('overall_risk', 'low'),
                'issues_count': len(issues),
                'has_guidance': len(guidance) > 0
            },
            'issues': [
                {
                    'field': issue.get('field', 'Unknown'),
                    'message': issue.get('message', 'No message'),
                    'risk_level': issue.get('risk_level', 'unknown'),
                    'category': issue.get('category', 'unknown')
                }
                for issue in issues
            ],
            'security_guidance': [
                {
                    'field': guide.get('field', 'Unknown'),
                    'message': guide.get('message', 'No message'),
                    'recommendations': guide.get('recommendations', [])
                }
                for guide in guidance
            ]
        }

    # Add errors if any
    if results.get('errors'):
        yaml_data['validation_errors'] = results['errors']

    # Write to YAML file
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            yaml.dump(yaml_data, f, default_flow_style=False, allow_unicode=True,
                     sort_keys=False, indent=2)

        return output_file

    except ImportError:
        # Fallback if PyYAML is not installed - create a simple YAML-like format
        yaml_content = f"""# Security Validation Results
metadata:
  generated: "{timestamp}"
  csv_file: "{csv_file}"
  validation_engine: "CSV Security Validator v1.0"
  total_flows: {results.get('total_flows', 0)}
  flows_with_issues: {results.get('flows_with_issues', 0)}

summary:
  critical_issues: {results.get('critical_issues', 0)}
  high_risk_issues: {results.get('high_risk_issues', 0)}
  medium_risk_issues: {results.get('medium_risk_issues', 0)}
  low_risk_issues: {results.get('low_risk_issues', 0)}
  compliance_rate: {round(((results.get('total_flows', 0) - results.get('flows_with_issues', 0)) / results.get('total_flows', 1) * 100), 2) if results.get('total_flows', 0) > 0 else 0}%

validation_summary:
"""

        # Add summary lines
        for line in results.get('summary', []):
            yaml_content += f'  - "{line}"\n'

        yaml_content += "\nflows:\n"

        # Add flow details
        for flow_id, flow_result in flow_results.items():
            flow_data = flow_result.get('flow_data', {})
            issues = flow_result.get('issues', [])

            yaml_content += f"  {flow_id}:\n"
            yaml_content += f"    flow_details:\n"
            yaml_content += f"      source_ip: \"{flow_data.get('Source IP', '')}\"\n"
            yaml_content += f"      destination_ip: \"{flow_data.get('Destination IP', '')}\"\n"
            yaml_content += f"      port: \"{flow_data.get('Port', '')}\"\n"
            yaml_content += f"      service: \"{flow_data.get('Service', '')}\"\n"
            yaml_content += f"      action: \"{flow_data.get('Action', '')}\"\n"

            yaml_content += f"    security_assessment:\n"
            yaml_content += f"      overall_risk: \"{flow_result.get('overall_risk', 'low')}\"\n"
            yaml_content += f"      issues_count: {len(issues)}\n"

            if issues:
                yaml_content += f"    issues:\n"
                for issue in issues:
                    yaml_content += f"      - field: \"{issue.get('field', 'Unknown')}\"\n"
                    yaml_content += f"        message: \"{issue.get('message', 'No message')}\"\n"
                    yaml_content += f"        risk_level: \"{issue.get('risk_level', 'unknown')}\"\n"

        # Add errors if any
        if results.get('errors'):
            yaml_content += "\nvalidation_errors:\n"
            for error in results['errors']:
                yaml_content += f'  - "{error}"\n'

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(yaml_content)

        return output_file


def main():
    """Example usage of the security validator."""
    # Load CSV data
    csv_file = "Example.csv"
    flow_data = csv_to_dict_simple(csv_file)
    
    # Initialize validator
    validator = SecurityValidator(
        unacceptable_values_file="unacceptable_values.json",
        guidance_file="security_guidance.json"
    )
    
    # Validate the data
    results = validator.validate_flow_data(flow_data)

    # Generate markdown report
    report_file = generate_markdown_report(results, flow_data, csv_file, "security_validation_report.md")
    print(f"Security validation completed. Report generated: {report_file}")

    # Print summary
    print("\n=== SECURITY VALIDATION SUMMARY ===")
    for summary_line in results["summary"]:
        print(summary_line)
    
    print(f"\n=== DETAILED RESULTS ===")
    for flow_id, flow_result in results["flow_results"].items():
        print(f"\n{flow_id.upper()}:")
        print(f"  Overall Risk: {flow_result['overall_risk'].upper()}")
        
        if flow_result["issues"]:
            print("  Issues Found:")
            for issue in flow_result["issues"]:
                print(f"    - {issue['message']} (Risk: {issue['risk_level']})")
        
        if flow_result["guidance"]:
            print("  Security Guidance:")
            for guidance in flow_result["guidance"]:
                print(f"    - {guidance['field']}: {guidance['message']}")
                if guidance["recommendations"]:
                    print(f"      Recommendations: {', '.join(guidance['recommendations'])}")
    
    return results


if __name__ == "__main__":
    main()
