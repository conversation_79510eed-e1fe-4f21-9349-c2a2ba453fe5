import json
from typing import Dict, List, Any, Tuple
from csv_processor import csv_to_dict_simple


class SecurityValidator:
    def __init__(self, unacceptable_values_file: str, guidance_file: str):
        """
        Initialize the security validator with reference files.
        
        Args:
            unacceptable_values_file: Path to JSON file with unacceptable values
            guidance_file: Path to JSON file with security guidance
        """
        self.unacceptable_values = self._load_json(unacceptable_values_file)
        self.guidance = self._load_json(guidance_file)
        
    def _load_json(self, file_path: str) -> Dict:
        """Load JSON file and return as dictionary."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Warning: File '{file_path}' not found.")
            return {}
        except json.JSONDecodeError as e:
            print(f"Error parsing JSON file '{file_path}': {e}")
            return {}
    
    def validate_flow_data(self, flow_data: Dict[str, Dict[str, str]]) -> Dict[str, Any]:
        """
        Validate flow data against unacceptable values and provide guidance.

        Args:
            flow_data: Dictionary of flow data from CSV

        Returns:
            Dictionary containing validation results and security guidance
        """
        # Initialize results with safe defaults
        validation_results = {
            "total_flows": 0,
            "flows_with_issues": 0,
            "critical_issues": 0,
            "high_risk_issues": 0,
            "medium_risk_issues": 0,
            "low_risk_issues": 0,
            "flow_results": {},
            "summary": [],
            "errors": []
        }

        # Validate input
        if not flow_data:
            validation_results["errors"].append("No flow data provided")
            validation_results["summary"].append("❌ No flow data to validate")
            return validation_results

        if not isinstance(flow_data, dict):
            validation_results["errors"].append(f"Expected dict, got {type(flow_data)}")
            validation_results["summary"].append("❌ Invalid flow data format")
            return validation_results

        # Set total flows after validation
        validation_results["total_flows"] = len(flow_data)

        try:
            for flow_id, flow_info in flow_data.items():
                try:
                    flow_result = self._validate_single_flow(flow_id, flow_info)
                    validation_results["flow_results"][flow_id] = flow_result

                    # Count issues by severity
                    if flow_result.get("issues"):
                        validation_results["flows_with_issues"] += 1
                        for issue in flow_result["issues"]:
                            risk_level = issue.get("risk_level", "unknown")
                            if risk_level == "critical":
                                validation_results["critical_issues"] += 1
                            elif risk_level == "high":
                                validation_results["high_risk_issues"] += 1
                            elif risk_level == "medium":
                                validation_results["medium_risk_issues"] += 1
                            elif risk_level == "low":
                                validation_results["low_risk_issues"] += 1

                except Exception as e:
                    error_msg = f"Error validating flow {flow_id}: {str(e)}"
                    validation_results["errors"].append(error_msg)
                    print(f"Warning: {error_msg}")

            # Generate summary
            validation_results["summary"] = self._generate_summary(validation_results)

        except Exception as e:
            error_msg = f"Error during validation: {str(e)}"
            validation_results["errors"].append(error_msg)
            validation_results["summary"].append(f"❌ Validation failed: {str(e)}")
            print(f"Error: {error_msg}")

        return validation_results
    
    def _validate_single_flow(self, flow_id: str, flow_info: Dict[str, str]) -> Dict[str, Any]:
        """Validate a single flow and return issues and guidance."""
        flow_result = {
            "flow_id": flow_id,
            "flow_data": flow_info,
            "issues": [],
            "guidance": [],
            "overall_risk": "low"
        }
        
        # Check each field in the flow
        for field, value in flow_info.items():
            if not value:  # Skip empty values
                continue
                
            # Check against unacceptable values
            field_issues = self._check_unacceptable_values(field, value)
            flow_result["issues"].extend(field_issues)
            
            # Get security guidance
            field_guidance = self._get_security_guidance(field, value)
            if field_guidance:
                flow_result["guidance"].append(field_guidance)
        
        # Determine overall risk level
        flow_result["overall_risk"] = self._determine_overall_risk(flow_result["issues"])
        
        return flow_result
    
    def _check_unacceptable_values(self, field: str, value: str) -> List[Dict[str, Any]]:
        """Check if a field value is in the unacceptable values list."""
        issues = []
        
        if field not in self.unacceptable_values:
            return issues
        
        field_rules = self.unacceptable_values[field]
        
        for category, values in field_rules.items():
            if isinstance(values, list) and value.lower() in [v.lower() for v in values]:
                issues.append({
                    "field": field,
                    "value": value,
                    "category": category,
                    "risk_level": self._map_category_to_risk(category),
                    "message": f"{field} '{value}' is classified as {category}"
                })
        
        return issues
    
    def _get_security_guidance(self, field: str, value: str) -> Dict[str, Any]:
        """Get security guidance for a specific field value."""
        guidance = None
        
        # Check service guidance
        if field.lower() == "service" and "service_guidance" in self.guidance:
            guidance = self.guidance["service_guidance"].get(value.lower())
        
        # Check port guidance  
        elif field.lower() == "port" and "port_guidance" in self.guidance:
            guidance = self.guidance["port_guidance"].get(value)
        
        # Check action guidance
        elif field.lower() == "action" and "action_guidance" in self.guidance:
            guidance = self.guidance["action_guidance"].get(value)
        
        # Default guidance if no specific guidance found
        if not guidance and "default_guidance" in self.guidance:
            guidance = self.guidance["default_guidance"]
        
        if guidance:
            return {
                "field": field,
                "value": value,
                "risk_level": guidance.get("risk_level", "unknown"),
                "message": guidance.get("message", "No guidance available"),
                "recommendations": guidance.get("recommendations", [])
            }
        
        return None
    
    def _map_category_to_risk(self, category: str) -> str:
        """Map unacceptable value categories to risk levels."""
        risk_mapping = {
            "blocked": "critical",
            "risky": "high", 
            "deprecated": "medium",
            "concerning": "medium",
            "suspicious": "high",
            "acceptable": "low"
        }
        return risk_mapping.get(category.lower(), "medium")
    
    def _determine_overall_risk(self, issues: List[Dict[str, Any]]) -> str:
        """Determine overall risk level based on all issues."""
        if not issues:
            return "low"
        
        risk_levels = [issue.get("risk_level", "low") for issue in issues]
        
        if "critical" in risk_levels:
            return "critical"
        elif "high" in risk_levels:
            return "high"
        elif "medium" in risk_levels:
            return "medium"
        else:
            return "low"
    
    def _generate_summary(self, results: Dict[str, Any]) -> List[str]:
        """Generate a summary of validation results."""
        summary = []
        
        total = results["total_flows"]
        issues = results["flows_with_issues"]
        
        summary.append(f"Analyzed {total} flows, {issues} flows have security issues")
        
        if results["critical_issues"] > 0:
            summary.append(f"🔴 CRITICAL: {results['critical_issues']} critical security issues found")
        
        if results["high_risk_issues"] > 0:
            summary.append(f"🟠 HIGH: {results['high_risk_issues']} high-risk issues found")
        
        if results["medium_risk_issues"] > 0:
            summary.append(f"🟡 MEDIUM: {results['medium_risk_issues']} medium-risk issues found")
        
        if results["low_risk_issues"] > 0:
            summary.append(f"🟢 LOW: {results['low_risk_issues']} low-risk issues found")
        
        if issues == 0:
            summary.append("✅ No security issues detected")
        
        return summary


def generate_markdown_report(results: Dict[str, Any], csv_file: str = "Example.csv",
                           output_file: str = "security_validation_report.md") -> str:
    """
    Generate a comprehensive markdown report from validation results.

    Args:
        results: Validation results from validate_flow_data()
        csv_file: Name of the CSV file that was validated
        output_file: Output markdown file path

    Returns:
        str: Path to generated report file
    """
    from datetime import datetime

    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    total_flows = results.get('total_flows', 0)
    flows_with_issues = results.get('flows_with_issues', 0)
    critical_issues = results.get('critical_issues', 0)
    high_risk_issues = results.get('high_risk_issues', 0)
    medium_risk_issues = results.get('medium_risk_issues', 0)
    low_risk_issues = results.get('low_risk_issues', 0)

    # Determine overall status
    if critical_issues > 0:
        status = "🔴 **CRITICAL**"
        status_desc = "Immediate action required"
    elif high_risk_issues > 0:
        status = "🟠 **HIGH RISK**"
        status_desc = "Significant security concerns identified"
    elif flows_with_issues > 0:
        status = "🟡 **MEDIUM RISK**"
        status_desc = "Some security issues require attention"
    else:
        status = "🟢 **SECURE**"
        status_desc = "No significant security issues detected"

    compliance_rate = ((total_flows - flows_with_issues) / total_flows * 100) if total_flows > 0 else 0

    # Build markdown report
    report_content = f"""# 🔒 CSV Security Validation Report

**Generated:** {timestamp}
**CSV File:** `{csv_file}`
**Validation Engine:** CSV Security Validator v1.0

---

## 📊 Executive Summary

### Overall Security Status: {status}
*{status_desc}*

### Key Metrics
| Metric | Value | Status |
|--------|-------|--------|
| **Total Flows Analyzed** | {total_flows} | ℹ️ |
| **Flows with Issues** | {flows_with_issues} | {'🔴' if flows_with_issues > 0 else '🟢'} |
| **Compliance Rate** | {compliance_rate:.1f}% | {'🟢' if compliance_rate >= 90 else '🟡' if compliance_rate >= 70 else '🔴'} |
| **Critical Issues** | {critical_issues} | {'🔴' if critical_issues > 0 else '🟢'} |
| **High Risk Issues** | {high_risk_issues} | {'🟠' if high_risk_issues > 0 else '🟢'} |
| **Medium Risk Issues** | {medium_risk_issues} | {'🟡' if medium_risk_issues > 0 else '🟢'} |
| **Low Risk Issues** | {low_risk_issues} | {'🟢' if low_risk_issues > 0 else '⚪'} |

## 🎯 Risk Level Breakdown

```
Critical: {critical_issues:>3} issues  {'█' * min(critical_issues, 20)}
High:     {high_risk_issues:>3} issues  {'█' * min(high_risk_issues, 20)}
Medium:   {medium_risk_issues:>3} issues  {'█' * min(medium_risk_issues, 20)}
Low:      {low_risk_issues:>3} issues  {'█' * min(low_risk_issues, 20)}
```

## 📋 Summary
"""

    # Add summary lines
    summary_lines = results.get('summary', [])
    for line in summary_lines:
        if "CRITICAL" in line:
            report_content += f"- 🔴 {line}\n"
        elif "HIGH" in line:
            report_content += f"- 🟠 {line}\n"
        elif "MEDIUM" in line:
            report_content += f"- 🟡 {line}\n"
        elif "LOW" in line:
            report_content += f"- 🟢 {line}\n"
        else:
            report_content += f"- ℹ️ {line}\n"

    # Add detailed findings
    flow_results = results.get('flow_results', {})
    if flow_results:
        report_content += "\n## 🔍 Detailed Findings\n\n"

        # Group by risk level
        risk_groups = {'critical': [], 'high': [], 'medium': [], 'low': []}
        for flow_id, flow_result in flow_results.items():
            if flow_result.get('issues'):
                risk_level = flow_result.get('overall_risk', 'low')
                risk_groups[risk_level].append((flow_id, flow_result))

        for risk_level, flows in risk_groups.items():
            if flows:
                risk_emoji = {'critical': '🔴', 'high': '🟠', 'medium': '🟡', 'low': '🟢'}
                report_content += f"### {risk_emoji[risk_level]} {risk_level.title()} Risk Issues\n\n"

                for flow_id, flow_result in flows:
                    flow_data = flow_result.get('flow_data', {})
                    issues = flow_result.get('issues', [])

                    report_content += f"#### {flow_id}\n\n"
                    report_content += "**Flow Details:**\n"
                    report_content += "| Field | Value |\n|-------|-------|\n"
                    for field, value in flow_data.items():
                        report_content += f"| {field} | `{value}` |\n"

                    report_content += "\n**Security Issues:**\n"
                    for issue in issues:
                        field = issue.get('field', 'Unknown')
                        message = issue.get('message', 'No message')
                        risk = issue.get('risk_level', 'unknown')
                        risk_emoji_issue = {'critical': '🔴', 'high': '🟠', 'medium': '🟡', 'low': '🟢'}.get(risk, '⚪')
                        report_content += f"- {risk_emoji_issue} **{field}**: {message}\n"

                    report_content += "\n"

    # Add errors if any
    errors = results.get('errors', [])
    if errors:
        report_content += "## ⚠️ Validation Errors\n\n"
        for error in errors:
            report_content += f"- {error}\n"

    # Add footer
    report_content += f"\n---\n*Report generated by CSV Security Validator - {timestamp}*\n"

    # Write to file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(report_content)

    return output_file


def export_csv_data_to_yaml(flow_data: Dict[str, Dict[str, str]],
                           output_file: str = "csv_data_export.yaml") -> str:
    """
    Export raw CSV data to a YAML file without validation results.

    Args:
        flow_data: Dictionary of flow data from csv_to_dict_simple()
        output_file: Output YAML file path

    Returns:
        str: Path to generated YAML file
    """
    import yaml
    from datetime import datetime

    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Build YAML structure for raw CSV data
    yaml_data = {
        'metadata': {
            'exported': timestamp,
            'total_flows': len(flow_data),
            'export_type': 'CSV Data Export'
        },
        'flows': {}
    }

    # Add flow data
    for flow_id, flow_details in flow_data.items():
        yaml_data['flows'][flow_id] = {
            'source_ip': flow_details.get('Source IP', ''),
            'destination_ip': flow_details.get('Destination IP', ''),
            'port': flow_details.get('Port', ''),
            'service': flow_details.get('Service', ''),
            'action': flow_details.get('Action', ''),
            # Add any additional fields from the CSV
            **{k.lower().replace(' ', '_'): v for k, v in flow_details.items()
               if k not in ['Source IP', 'Destination IP', 'Port', 'Service', 'Action']}
        }

    # Write to YAML file
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            yaml.dump(yaml_data, f, default_flow_style=False, allow_unicode=True,
                     sort_keys=False, indent=2)
        return output_file

    except ImportError:
        # Fallback if PyYAML is not installed
        yaml_content = f"""# CSV Data Export
metadata:
  exported: "{timestamp}"
  total_flows: {len(flow_data)}
  export_type: "CSV Data Export"

flows:
"""

        for flow_id, flow_details in flow_data.items():
            yaml_content += f"  {flow_id}:\n"
            yaml_content += f"    source_ip: \"{flow_details.get('Source IP', '')}\"\n"
            yaml_content += f"    destination_ip: \"{flow_details.get('Destination IP', '')}\"\n"
            yaml_content += f"    port: \"{flow_details.get('Port', '')}\"\n"
            yaml_content += f"    service: \"{flow_details.get('Service', '')}\"\n"
            yaml_content += f"    action: \"{flow_details.get('Action', '')}\"\n"

            # Add additional fields
            for k, v in flow_details.items():
                if k not in ['Source IP', 'Destination IP', 'Port', 'Service', 'Action']:
                    field_name = k.lower().replace(' ', '_')
                    yaml_content += f"    {field_name}: \"{v}\"\n"

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(yaml_content)

        return output_file


def export_to_yaml(results: Dict[str, Any], csv_file: str = "Example.csv",
                   output_file: str = "security_validation_results.yaml") -> str:
    """
    Export validation results and CSV data to a YAML file.

    Args:
        results: Validation results from validate_flow_data()
        csv_file: Name of the CSV file that was validated
        output_file: Output YAML file path

    Returns:
        str: Path to generated YAML file
    """
    import yaml
    from datetime import datetime

    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Build YAML structure
    yaml_data = {
        'metadata': {
            'generated': timestamp,
            'csv_file': csv_file,
            'validation_engine': 'CSV Security Validator v1.0',
            'total_flows': results.get('total_flows', 0),
            'flows_with_issues': results.get('flows_with_issues', 0)
        },
        'summary': {
            'critical_issues': results.get('critical_issues', 0),
            'high_risk_issues': results.get('high_risk_issues', 0),
            'medium_risk_issues': results.get('medium_risk_issues', 0),
            'low_risk_issues': results.get('low_risk_issues', 0),
            'compliance_rate': round(
                ((results.get('total_flows', 0) - results.get('flows_with_issues', 0)) /
                 results.get('total_flows', 1) * 100), 2
            ) if results.get('total_flows', 0) > 0 else 0
        },
        'validation_summary': results.get('summary', []),
        'flows': {}
    }

    # Add flow details
    flow_results = results.get('flow_results', {})
    for flow_id, flow_result in flow_results.items():
        flow_data = flow_result.get('flow_data', {})
        issues = flow_result.get('issues', [])
        guidance = flow_result.get('guidance', [])

        yaml_data['flows'][flow_id] = {
            'flow_details': {
                'source_ip': flow_data.get('Source IP', ''),
                'destination_ip': flow_data.get('Destination IP', ''),
                'port': flow_data.get('Port', ''),
                'service': flow_data.get('Service', ''),
                'action': flow_data.get('Action', ''),
                # Add any additional fields from the CSV
                **{k.lower().replace(' ', '_'): v for k, v in flow_data.items()
                   if k not in ['Source IP', 'Destination IP', 'Port', 'Service', 'Action']}
            },
            'security_assessment': {
                'overall_risk': flow_result.get('overall_risk', 'low'),
                'issues_count': len(issues),
                'has_guidance': len(guidance) > 0
            },
            'issues': [
                {
                    'field': issue.get('field', 'Unknown'),
                    'message': issue.get('message', 'No message'),
                    'risk_level': issue.get('risk_level', 'unknown'),
                    'category': issue.get('category', 'unknown')
                }
                for issue in issues
            ],
            'security_guidance': [
                {
                    'field': guide.get('field', 'Unknown'),
                    'message': guide.get('message', 'No message'),
                    'recommendations': guide.get('recommendations', [])
                }
                for guide in guidance
            ]
        }

    # Add errors if any
    if results.get('errors'):
        yaml_data['validation_errors'] = results['errors']

    # Write to YAML file
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            yaml.dump(yaml_data, f, default_flow_style=False, allow_unicode=True,
                     sort_keys=False, indent=2)

        return output_file

    except ImportError:
        # Fallback if PyYAML is not installed - create a simple YAML-like format
        yaml_content = f"""# Security Validation Results
metadata:
  generated: "{timestamp}"
  csv_file: "{csv_file}"
  validation_engine: "CSV Security Validator v1.0"
  total_flows: {results.get('total_flows', 0)}
  flows_with_issues: {results.get('flows_with_issues', 0)}

summary:
  critical_issues: {results.get('critical_issues', 0)}
  high_risk_issues: {results.get('high_risk_issues', 0)}
  medium_risk_issues: {results.get('medium_risk_issues', 0)}
  low_risk_issues: {results.get('low_risk_issues', 0)}
  compliance_rate: {round(((results.get('total_flows', 0) - results.get('flows_with_issues', 0)) / results.get('total_flows', 1) * 100), 2) if results.get('total_flows', 0) > 0 else 0}%

validation_summary:
"""

        # Add summary lines
        for line in results.get('summary', []):
            yaml_content += f'  - "{line}"\n'

        yaml_content += "\nflows:\n"

        # Add flow details
        for flow_id, flow_result in flow_results.items():
            flow_data = flow_result.get('flow_data', {})
            issues = flow_result.get('issues', [])

            yaml_content += f"  {flow_id}:\n"
            yaml_content += f"    flow_details:\n"
            yaml_content += f"      source_ip: \"{flow_data.get('Source IP', '')}\"\n"
            yaml_content += f"      destination_ip: \"{flow_data.get('Destination IP', '')}\"\n"
            yaml_content += f"      port: \"{flow_data.get('Port', '')}\"\n"
            yaml_content += f"      service: \"{flow_data.get('Service', '')}\"\n"
            yaml_content += f"      action: \"{flow_data.get('Action', '')}\"\n"

            yaml_content += f"    security_assessment:\n"
            yaml_content += f"      overall_risk: \"{flow_result.get('overall_risk', 'low')}\"\n"
            yaml_content += f"      issues_count: {len(issues)}\n"

            if issues:
                yaml_content += f"    issues:\n"
                for issue in issues:
                    yaml_content += f"      - field: \"{issue.get('field', 'Unknown')}\"\n"
                    yaml_content += f"        message: \"{issue.get('message', 'No message')}\"\n"
                    yaml_content += f"        risk_level: \"{issue.get('risk_level', 'unknown')}\"\n"

        # Add errors if any
        if results.get('errors'):
            yaml_content += "\nvalidation_errors:\n"
            for error in results['errors']:
                yaml_content += f'  - "{error}"\n'

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(yaml_content)

        return output_file


def main():
    """Example usage of the security validator."""
    # Load CSV data
    csv_file = "Example.csv"
    flow_data = csv_to_dict_simple(csv_file)
    
    # Initialize validator
    validator = SecurityValidator(
        unacceptable_values_file="unacceptable_values.json",
        guidance_file="security_guidance.json"
    )
    
    # Validate the data
    results = validator.validate_flow_data(flow_data)

    # Generate markdown report
    report_file = generate_markdown_report(results, csv_file, "security_validation_report.md")
    print(f"Security validation completed. Report generated: {report_file}")

    # Generate YAML export
    yaml_file = export_to_yaml(results, csv_file, "security_validation_results.yaml")
    print(f"YAML export generated: {yaml_file}")

    # Print summary
    print("\n=== SECURITY VALIDATION SUMMARY ===")
    for summary_line in results["summary"]:
        print(summary_line)
    
    print(f"\n=== DETAILED RESULTS ===")
    for flow_id, flow_result in results["flow_results"].items():
        print(f"\n{flow_id.upper()}:")
        print(f"  Overall Risk: {flow_result['overall_risk'].upper()}")
        
        if flow_result["issues"]:
            print("  Issues Found:")
            for issue in flow_result["issues"]:
                print(f"    - {issue['message']} (Risk: {issue['risk_level']})")
        
        if flow_result["guidance"]:
            print("  Security Guidance:")
            for guidance in flow_result["guidance"]:
                print(f"    - {guidance['field']}: {guidance['message']}")
                if guidance["recommendations"]:
                    print(f"      Recommendations: {', '.join(guidance['recommendations'])}")
    
    return results


if __name__ == "__main__":
    main()
