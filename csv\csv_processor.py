import csv
import re
import ipaddress
from typing import List, Dict, Any, <PERSON><PERSON>


def validate_and_fix_data(field_name: str, value: str) -> Tuple[str, List[str]]:
    """
    Validate and fix common data format issues.

    Args:
        field_name: Name of the field being validated
        value: The value to validate and potentially fix

    Returns:
        Tuple of (fixed_value, list_of_warnings)
    """
    warnings = []
    fixed_value = value.strip()

    # Normalize field name for comparison
    field_lower = field_name.lower().replace(" ", "").replace("_", "")

    # IP Address validation and fixing
    if "ip" in field_lower or "address" in field_lower:
        if fixed_value:
            # Remove common prefixes/suffixes
            ip_clean = re.sub(r'^(ip:|addr:|address:)\s*', '', fixed_value, flags=re.IGNORECASE)
            ip_clean = re.sub(r'\s*(;|,|:).*$', '', ip_clean)  # Remove anything after ; , :

            try:
                # Validate IP address
                ipaddress.ip_address(ip_clean)
                if ip_clean != fixed_value:
                    warnings.append(f"Cleaned IP address: '{fixed_value}' → '{ip_clean}'")
                    fixed_value = ip_clean
            except ValueError:
                # Try to extract IP from string
                ip_pattern = r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
                ip_match = re.search(ip_pattern, fixed_value)
                if ip_match:
                    extracted_ip = ip_match.group()
                    try:
                        ipaddress.ip_address(extracted_ip)
                        warnings.append(f"Extracted IP from: '{fixed_value}' → '{extracted_ip}'")
                        fixed_value = extracted_ip
                    except ValueError:
                        warnings.append(f"Invalid IP address format: '{fixed_value}'")
                else:
                    warnings.append(f"Could not parse IP address: '{fixed_value}'")

    # Port validation and fixing
    elif "port" in field_lower:
        if fixed_value:
            # Extract numeric port
            port_match = re.search(r'\b(\d+)\b', fixed_value)
            if port_match:
                port_num = int(port_match.group(1))
                if 1 <= port_num <= 65535:
                    if str(port_num) != fixed_value:
                        warnings.append(f"Extracted port number: '{fixed_value}' → '{port_num}'")
                        fixed_value = str(port_num)
                else:
                    warnings.append(f"Port number out of range (1-65535): {port_num}")
            else:
                warnings.append(f"Could not parse port number: '{fixed_value}'")

    # Service/Protocol validation and fixing
    elif "service" in field_lower or "protocol" in field_lower:
        if fixed_value:
            # Normalize common service names
            service_map = {
                'http': 'http', 'https': 'https', 'ftp': 'ftp', 'ftps': 'ftps',
                'ssh': 'ssh', 'telnet': 'telnet', 'smtp': 'smtp', 'pop3': 'pop3',
                'imap': 'imap', 'dns': 'dns', 'dhcp': 'dhcp', 'snmp': 'snmp',
                'tcp': 'tcp', 'udp': 'udp', 'icmp': 'icmp'
            }

            service_lower = fixed_value.lower().strip()
            if service_lower in service_map:
                normalized = service_map[service_lower]
                if normalized != fixed_value:
                    warnings.append(f"Normalized service: '{fixed_value}' → '{normalized}'")
                    fixed_value = normalized

    # Action validation and fixing
    elif "action" in field_lower:
        if fixed_value:
            action_map = {
                'allow': 'Allow', 'allowed': 'Allow', 'accept': 'Allow', 'permit': 'Allow',
                'deny': 'Deny', 'denied': 'Deny', 'block': 'Deny', 'reject': 'Deny',
                'drop': 'Drop', 'dropped': 'Drop'
            }

            action_lower = fixed_value.lower().strip()
            if action_lower in action_map:
                normalized = action_map[action_lower]
                if normalized != fixed_value:
                    warnings.append(f"Normalized action: '{fixed_value}' → '{normalized}'")
                    fixed_value = normalized

    # File Type validation and fixing
    elif "file" in field_lower and "type" in field_lower:
        if fixed_value:
            # Remove dots and normalize extensions
            file_types = []
            for ft in fixed_value.split(','):
                ft_clean = ft.strip().lower()
                # Remove leading dots
                ft_clean = re.sub(r'^\.+', '', ft_clean)
                if ft_clean:
                    file_types.append(ft_clean)

            if file_types:
                normalized = ','.join(file_types)
                if normalized != fixed_value.lower():
                    warnings.append(f"Normalized file types: '{fixed_value}' → '{normalized}'")
                    fixed_value = normalized

    return fixed_value, warnings


def validate_csv_structure(flow_data: Dict[str, Dict[str, str]]) -> Tuple[Dict[str, Dict[str, str]], List[str]]:
    """
    Validate and fix the overall CSV structure.

    Args:
        flow_data: The loaded flow data

    Returns:
        Tuple of (fixed_flow_data, list_of_issues)
    """
    issues = []
    fixed_data = {}

    if not flow_data:
        issues.append("❌ No flow data found - check if CSV has data rows starting with 'Flow'")
        return {}, issues

    issues.append(f"✅ Found {len(flow_data)} flows")

    # Check for required columns
    sample_flow = next(iter(flow_data.values()))
    columns = list(sample_flow.keys())

    # Check for common required columns
    expected_columns = {
        'source': ['Source IP', 'Source', 'Src IP', 'Src', 'Source Address'],
        'destination': ['Destination IP', 'Destination', 'Dest IP', 'Dest', 'Destination Address'],
        'port': ['Port', 'Dest Port', 'Destination Port', 'Target Port'],
        'service': ['Service', 'Protocol', 'Service/Protocol'],
        'action': ['Action', 'Decision', 'Result'],
        'filetype': ['File Type', 'FileType', 'File_Type', 'Extension', 'File Extension']
    }

    found_columns = {}
    for category, possible_names in expected_columns.items():
        for col_name in columns:
            if col_name in possible_names:
                found_columns[category] = col_name
                break

    # Report column findings
    for category, col_name in found_columns.items():
        issues.append(f"✅ Found {category} column: '{col_name}'")

    missing_categories = set(expected_columns.keys()) - set(found_columns.keys())
    for category in missing_categories:
        possible = expected_columns[category]
        issues.append(f"⚠️  No {category} column found. Expected one of: {possible}")

    # Validate and fix data in each flow
    total_warnings = 0
    for flow_id, flow_data_item in flow_data.items():
        fixed_flow = {}
        flow_warnings = []

        for field_name, value in flow_data_item.items():
            fixed_value, warnings = validate_and_fix_data(field_name, value)
            fixed_flow[field_name] = fixed_value
            flow_warnings.extend(warnings)

        if flow_warnings:
            total_warnings += len(flow_warnings)
            issues.append(f"🔧 {flow_id}: {len(flow_warnings)} fixes applied")
            for warning in flow_warnings[:3]:  # Show first 3 warnings per flow
                issues.append(f"   • {warning}")
            if len(flow_warnings) > 3:
                issues.append(f"   • ... and {len(flow_warnings) - 3} more fixes")

        fixed_data[flow_id] = fixed_flow

    if total_warnings > 0:
        issues.append(f"🔧 Total data fixes applied: {total_warnings}")
    else:
        issues.append(f"✅ All data appears to be in correct format")

    return fixed_data, issues


def find_data_table_start(csv_file_path: str) -> Tuple[int, List[str]]:
    """
    Automatically find where the actual data table starts by looking for Flow rows.

    Args:
        csv_file_path: Path to the CSV file

    Returns:
        Tuple of (header_row_index, headers_list)
    """
    try:
        with open(csv_file_path, 'r', newline='', encoding='utf-8') as csvfile:
            csv_reader = csv.reader(csvfile)

            for row_index, row in enumerate(csv_reader):
                if row and len(row) > 0:
                    # Check if this row has a Flow entry in the first column
                    first_col = row[0].strip().lower()
                    if first_col.startswith('flow'):
                        # Found a Flow row, so the previous row should be headers
                        # Go back and get the header row
                        csvfile.seek(0)  # Reset file pointer
                        csv_reader = csv.reader(csvfile)

                        # Skip to the row before the Flow row
                        header_row_index = max(0, row_index - 1)
                        for i in range(header_row_index + 1):
                            header_row = next(csv_reader)

                        # Clean headers and return
                        clean_headers = [header.strip() for header in header_row if header.strip()]

                        print(f"🔍 Auto-detected data table:")
                        print(f"   Header row: {header_row_index + 1}")
                        print(f"   First Flow row: {row_index + 1}")
                        print(f"   Headers: {clean_headers}")

                        return header_row_index, clean_headers

            # If no Flow rows found, try to find a reasonable header row
            # Look for rows that contain common column names
            csvfile.seek(0)
            csv_reader = csv.reader(csvfile)

            common_headers = ['source', 'destination', 'ip', 'port', 'service', 'protocol', 'action', 'file', 'type']

            for row_index, row in enumerate(csv_reader):
                if row and len(row) > 1:
                    # Check if this row looks like headers
                    row_text = ' '.join(row).lower()
                    header_score = sum(1 for header in common_headers if header in row_text)

                    if header_score >= 3:  # At least 3 common header terms
                        clean_headers = [header.strip() for header in row if header.strip()]

                        print(f"🔍 Auto-detected header row (no Flow rows found):")
                        print(f"   Header row: {row_index + 1}")
                        print(f"   Headers: {clean_headers}")
                        print(f"   ⚠️  No Flow rows detected - will process all data rows")

                        return row_index, clean_headers

            print(f"❌ Could not auto-detect data table structure")
            return 0, []

    except Exception as e:
        print(f"❌ Error auto-detecting table structure: {e}")
        return 0, []


def csv_to_dict_simple(csv_file_path: str, validate_data: bool = True) -> Dict[str, Dict[str, str]]:
    """
    Simple function to read CSV and return flow rows as dictionaries.
    Uses automatic table detection to find data regardless of header content amount.

    Args:
        csv_file_path (str): Path to the CSV file
        validate_data (bool): Whether to validate and fix data format issues (default: True)

    Returns:
        Dict[str, Dict[str, str]]: Dictionary with flow IDs as keys and flow data as values
    """
    flow_rows = {}

    try:
        # Auto-detect table structure
        header_row_index, clean_headers = find_data_table_start(csv_file_path)

        if not clean_headers:
            print(f"❌ Could not auto-detect table structure.")
            print(f"   • Ensure the CSV has rows starting with 'Flow' (any case)")
            print(f"   • Check that there are proper column headers before the Flow rows")
            print(f"   • Verify the file format is correct")
            return {}

        # Read the file starting from the detected position
        with open(csv_file_path, 'r', newline='', encoding='utf-8') as csvfile:
            csv_reader = csv.reader(csvfile)

            # Skip to the first Flow row (header_row_index + 1)
            for _ in range(header_row_index + 1):
                next(csv_reader)

            # Process Flow rows
            flow_counter = 1
            for row in csv_reader:
                if row and len(row) > 0:
                    # More flexible flow detection - handle "flows", "Flow", "FLOW", etc.
                    first_col = row[0].strip().lower()
                    if first_col.startswith('flow'):
                        # Create dictionary mapping headers to values
                        # Skip the first column (Flow identifier) and map remaining columns
                        row_dict = {}
                        for i, header in enumerate(clean_headers):
                            # Offset by 1 to skip the Flow column
                            if i + 1 < len(row):
                                row_dict[header] = row[i + 1].strip()

                        # Add to flow_rows dict with flow_X as key
                        flow_key = f"flow_{flow_counter}"
                        flow_rows[flow_key] = row_dict
                        flow_counter += 1

    except FileNotFoundError:
        print(f"❌ Error: File '{csv_file_path}' not found.")
        return {}
    except Exception as e:
        print(f"❌ Error reading CSV file: {e}")
        return {}

    # Apply data validation and fixing if requested
    if validate_data and flow_rows:
        print(f"\n🔍 Validating and fixing data format issues...")
        validated_data, validation_issues = validate_csv_structure(flow_rows)

        # Print validation results
        for issue in validation_issues:
            print(f"  {issue}")

        return validated_data

    return flow_rows


def process_csv_to_dict(csv_file_path: str) -> List[Dict[str, Any]]:
    """
    Iterate through a CSV file and map each row's values to their column headers.
    
    Args:
        csv_file_path (str): Path to the CSV file
        
    Returns:
        List[Dict[str, Any]]: List of dictionaries where each dict represents a row
                              with column headers as keys and row values as values
    """
    result_list = []
    
    try:
        with open(csv_file_path, 'r', newline='', encoding='utf-8') as csvfile:
            # Create a CSV reader that will automatically handle the header
            csv_reader = csv.DictReader(csvfile)
            
            # Iterate through each row in the CSV
            for row in csv_reader:
                # Each row is already a dictionary with headers as keys
                # Remove any leading/trailing whitespace from keys and values
                cleaned_row = {key.strip(): value.strip() for key, value in row.items()}
                result_list.append(cleaned_row)
                
    except FileNotFoundError:
        print(f"Error: File '{csv_file_path}' not found.")
        return []
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return []
    
    return result_list


def process_csv_flow_rows(csv_file_path: str) -> List[Dict[str, Any]]:
    """
    Process CSV file and return only rows that start with 'Flow' in the first column.

    Args:
        csv_file_path (str): Path to the CSV file

    Returns:
        List[Dict[str, Any]]: List of dictionaries for flow rows only
    """
    all_rows = process_csv_to_dict(csv_file_path)

    # Filter for rows where the first column value starts with 'Flow'
    flow_rows = []
    for row in all_rows:
        # Get the first column value (check for empty key or first key)
        first_column_key = list(row.keys())[0] if row else None
        if first_column_key is not None:
            first_value = row[first_column_key]
            if first_value and str(first_value).startswith('Flow'):
                # Remove the 'Flow' identifier from the dictionary since it's not needed
                # Create a clean dictionary without the flow identifier column
                clean_row = {k: v for k, v in row.items() if k != first_column_key or k.strip()}
                if first_column_key == '' or not first_column_key.strip():
                    # If the first column has no header, exclude it from the result
                    clean_row = {k: v for k, v in row.items() if k.strip()}
                else:
                    clean_row = row.copy()
                flow_rows.append(clean_row)

    return flow_rows


# Example usage
if __name__ == "__main__":
    # Process the CSV file
    csv_path = "Example.csv"

    print("=== Using Simple Function (Recommended) ===")
    simple_data = csv_to_dict_simple(csv_path)
    for flow_key, row in simple_data.items():
        print(f"{flow_key}: {row}")

    print("\n=== Example of accessing individual values ===")
    if simple_data:
        first_flow_key = list(simple_data.keys())[0]
        first_flow = simple_data[first_flow_key]
        print(f"First flow ({first_flow_key}):")
        print(f"Source IP: {first_flow.get('Source IP')}")
        print(f"Destination IP: {first_flow.get('Destination IP')}")
        print(f"Port: {first_flow.get('Port')}")
        print(f"Service: {first_flow.get('Service')}")
        print(f"Action: {first_flow.get('Action')}")

    print("\n=== Using DictReader Function ===")
    all_data = process_csv_to_dict(csv_path)
    for i, row in enumerate(all_data, 1):
        print(f"Row {i}: {row}")

    print("\nFlow rows only (DictReader):")
    flow_data = process_csv_flow_rows(csv_path)
    for i, row in enumerate(flow_data, 1):
        print(f"Flow Row {i}: {row}")
