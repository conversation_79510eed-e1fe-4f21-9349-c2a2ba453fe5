import csv
from typing import List, Dict, Any


def csv_to_dict_simple(csv_file_path: str) -> Dict[str, Dict[str, str]]:
    """
    Simple function to read CSV and return flow rows as dictionaries.
    Specifically designed for your use case.

    Args:
        csv_file_path (str): Path to the CSV file

    Returns:
        Dict[str, Dict[str, str]]: Dictionary with flow IDs as keys and flow data as values
    """
    flow_rows = {}

    try:
        with open(csv_file_path, 'r', newline='', encoding='utf-8') as csvfile:
            csv_reader = csv.reader(csvfile)

            # Read the header row
            headers = next(csv_reader)
            # Clean headers and skip empty first column if it exists
            clean_headers = [header.strip() for header in headers if header.strip()]

            # Process each data row
            flow_counter = 1
            for row in csv_reader:
                if row and len(row) > 0:
                    # Check if this is a flow row (first column contains 'Flow')
                    if row[0].strip().startswith('Flow'):
                        # Create dictionary mapping headers to values
                        # Skip the first column (Flow identifier) and map remaining columns
                        row_dict = {}
                        for i, header in enumerate(clean_headers):
                            # Offset by 1 to skip the Flow column
                            if i + 1 < len(row):
                                row_dict[header] = row[i + 1].strip()

                        # Add to flow_rows dict with flow_X as key
                        flow_key = f"flow_{flow_counter}"
                        flow_rows[flow_key] = row_dict
                        flow_counter += 1

    except FileNotFoundError:
        print(f"Error: File '{csv_file_path}' not found.")
        return {}
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return {}

    return flow_rows


def process_csv_to_dict(csv_file_path: str) -> List[Dict[str, Any]]:
    """
    Iterate through a CSV file and map each row's values to their column headers.
    
    Args:
        csv_file_path (str): Path to the CSV file
        
    Returns:
        List[Dict[str, Any]]: List of dictionaries where each dict represents a row
                              with column headers as keys and row values as values
    """
    result_list = []
    
    try:
        with open(csv_file_path, 'r', newline='', encoding='utf-8') as csvfile:
            # Create a CSV reader that will automatically handle the header
            csv_reader = csv.DictReader(csvfile)
            
            # Iterate through each row in the CSV
            for row in csv_reader:
                # Each row is already a dictionary with headers as keys
                # Remove any leading/trailing whitespace from keys and values
                cleaned_row = {key.strip(): value.strip() for key, value in row.items()}
                result_list.append(cleaned_row)
                
    except FileNotFoundError:
        print(f"Error: File '{csv_file_path}' not found.")
        return []
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return []
    
    return result_list


def process_csv_flow_rows(csv_file_path: str) -> List[Dict[str, Any]]:
    """
    Process CSV file and return only rows that start with 'Flow' in the first column.

    Args:
        csv_file_path (str): Path to the CSV file

    Returns:
        List[Dict[str, Any]]: List of dictionaries for flow rows only
    """
    all_rows = process_csv_to_dict(csv_file_path)

    # Filter for rows where the first column value starts with 'Flow'
    flow_rows = []
    for row in all_rows:
        # Get the first column value (check for empty key or first key)
        first_column_key = list(row.keys())[0] if row else None
        if first_column_key is not None:
            first_value = row[first_column_key]
            if first_value and str(first_value).startswith('Flow'):
                # Remove the 'Flow' identifier from the dictionary since it's not needed
                # Create a clean dictionary without the flow identifier column
                clean_row = {k: v for k, v in row.items() if k != first_column_key or k.strip()}
                if first_column_key == '' or not first_column_key.strip():
                    # If the first column has no header, exclude it from the result
                    clean_row = {k: v for k, v in row.items() if k.strip()}
                else:
                    clean_row = row.copy()
                flow_rows.append(clean_row)

    return flow_rows


# Example usage
if __name__ == "__main__":
    # Process the CSV file
    csv_path = "Example.csv"

    print("=== Using Simple Function (Recommended) ===")
    simple_data = csv_to_dict_simple(csv_path)
    for flow_key, row in simple_data.items():
        print(f"{flow_key}: {row}")

    print("\n=== Example of accessing individual values ===")
    if simple_data:
        first_flow_key = list(simple_data.keys())[0]
        first_flow = simple_data[first_flow_key]
        print(f"First flow ({first_flow_key}):")
        print(f"Source IP: {first_flow.get('Source IP')}")
        print(f"Destination IP: {first_flow.get('Destination IP')}")
        print(f"Port: {first_flow.get('Port')}")
        print(f"Service: {first_flow.get('Service')}")
        print(f"Action: {first_flow.get('Action')}")

    print("\n=== Using DictReader Function ===")
    all_data = process_csv_to_dict(csv_path)
    for i, row in enumerate(all_data, 1):
        print(f"Row {i}: {row}")

    print("\nFlow rows only (DictReader):")
    flow_data = process_csv_flow_rows(csv_path)
    for i, row in enumerate(flow_data, 1):
        print(f"Flow Row {i}: {row}")
