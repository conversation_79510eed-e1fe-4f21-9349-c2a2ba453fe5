"""
Comprehensive markdown report generator for CSV security validation results.
"""

import json
import os
from datetime import datetime
from typing import Dict, Any, List
from csv_processor import csv_to_dict_simple
from security_validator import SecurityValidator


class SecurityReportGenerator:
    def __init__(self, results: Dict[str, Any], csv_file: str, config_files: Dict[str, str]):
        """
        Initialize the report generator.
        
        Args:
            results: Validation results from SecurityValidator
            csv_file: Path to the CSV file that was validated
            config_files: Dictionary with paths to config files
        """
        self.results = results
        self.csv_file = csv_file
        self.config_files = config_files
        self.timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def generate_report(self, output_file: str = "security_validation_report.md") -> str:
        """Generate comprehensive markdown report."""
        
        report_sections = [
            self._generate_header(),
            self._generate_executive_summary(),
            self._generate_risk_overview(),
            self._generate_detailed_findings(),
            self._generate_flow_analysis(),
            self._generate_recommendations(),
            self._generate_technical_details(),
            self._generate_appendix()
        ]
        
        report_content = "\n\n".join(report_sections)
        
        # Write to file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        return output_file
    
    def _generate_header(self) -> str:
        """Generate report header."""
        return f"""# 🔒 CSV Security Validation Report

**Generated:** {self.timestamp}  
**CSV File:** `{self.csv_file}`  
**Validation Engine:** CSV Security Validator v1.0  

---"""
    
    def _generate_executive_summary(self) -> str:
        """Generate executive summary."""
        total_flows = self.results.get('total_flows', 0)
        flows_with_issues = self.results.get('flows_with_issues', 0)
        critical_issues = self.results.get('critical_issues', 0)
        high_risk_issues = self.results.get('high_risk_issues', 0)
        
        # Determine overall status
        if critical_issues > 0:
            status = "🔴 **CRITICAL**"
            status_desc = "Immediate action required"
        elif high_risk_issues > 0:
            status = "🟠 **HIGH RISK**"
            status_desc = "Significant security concerns identified"
        elif flows_with_issues > 0:
            status = "🟡 **MEDIUM RISK**"
            status_desc = "Some security issues require attention"
        else:
            status = "🟢 **SECURE**"
            status_desc = "No significant security issues detected"
        
        compliance_rate = ((total_flows - flows_with_issues) / total_flows * 100) if total_flows > 0 else 0
        
        return f"""## 📊 Executive Summary

### Overall Security Status: {status}
*{status_desc}*

### Key Metrics
| Metric | Value | Status |
|--------|-------|--------|
| **Total Flows Analyzed** | {total_flows} | ℹ️ |
| **Flows with Issues** | {flows_with_issues} | {'🔴' if flows_with_issues > 0 else '🟢'} |
| **Compliance Rate** | {compliance_rate:.1f}% | {'🟢' if compliance_rate >= 90 else '🟡' if compliance_rate >= 70 else '🔴'} |
| **Critical Issues** | {critical_issues} | {'🔴' if critical_issues > 0 else '🟢'} |
| **High Risk Issues** | {high_risk_issues} | {'🟠' if high_risk_issues > 0 else '🟢'} |

### Summary
{self._format_summary_text()}"""
    
    def _format_summary_text(self) -> str:
        """Format the summary text from results."""
        summary_lines = self.results.get('summary', [])
        if not summary_lines:
            return "No summary available."
        
        formatted_lines = []
        for line in summary_lines:
            if "CRITICAL" in line:
                formatted_lines.append(f"🔴 {line}")
            elif "HIGH" in line:
                formatted_lines.append(f"🟠 {line}")
            elif "MEDIUM" in line:
                formatted_lines.append(f"🟡 {line}")
            elif "LOW" in line:
                formatted_lines.append(f"🟢 {line}")
            else:
                formatted_lines.append(f"ℹ️ {line}")
        
        return "\n".join([f"- {line}" for line in formatted_lines])
    
    def _generate_risk_overview(self) -> str:
        """Generate risk level breakdown."""
        critical = self.results.get('critical_issues', 0)
        high = self.results.get('high_risk_issues', 0)
        medium = self.results.get('medium_risk_issues', 0)
        low = self.results.get('low_risk_issues', 0)
        
        return f"""## 🎯 Risk Level Breakdown

```
Critical: {critical:>3} issues  {'█' * min(critical, 20)}
High:     {high:>3} issues  {'█' * min(high, 20)}
Medium:   {medium:>3} issues  {'█' * min(medium, 20)}
Low:      {low:>3} issues  {'█' * min(low, 20)}
```

### Risk Level Definitions
| Level | Description | Action Required |
|-------|-------------|-----------------|
| 🔴 **Critical** | Blocked protocols/services that pose immediate security threats | **Immediate remediation** |
| 🟠 **High** | Risky protocols that require careful monitoring and controls | **Review and implement controls** |
| 🟡 **Medium** | Deprecated or concerning practices that should be addressed | **Plan for remediation** |
| 🟢 **Low** | Minor issues or informational findings | **Monitor and document** |"""
    
    def _generate_detailed_findings(self) -> str:
        """Generate detailed findings by risk level."""
        flow_results = self.results.get('flow_results', {})
        
        # Group flows by risk level
        risk_groups = {
            'critical': [],
            'high': [],
            'medium': [],
            'low': []
        }
        
        for flow_id, flow_result in flow_results.items():
            risk_level = flow_result.get('overall_risk', 'low')
            if flow_result.get('issues'):
                risk_groups[risk_level].append((flow_id, flow_result))
        
        sections = ["## 🔍 Detailed Security Findings"]
        
        for risk_level, flows in risk_groups.items():
            if not flows:
                continue
            
            risk_emoji = {'critical': '🔴', 'high': '🟠', 'medium': '🟡', 'low': '🟢'}
            sections.append(f"### {risk_emoji[risk_level]} {risk_level.title()} Risk Issues")
            
            for flow_id, flow_result in flows:
                sections.append(self._format_flow_finding(flow_id, flow_result))
        
        return "\n\n".join(sections)
    
    def _format_flow_finding(self, flow_id: str, flow_result: Dict[str, Any]) -> str:
        """Format individual flow finding."""
        flow_data = flow_result.get('flow_data', {})
        issues = flow_result.get('issues', [])
        
        # Create flow details table
        flow_details = []
        for field, value in flow_data.items():
            flow_details.append(f"| {field} | `{value}` |")
        
        # Create issues list
        issues_list = []
        for issue in issues:
            field = issue.get('field', 'Unknown')
            message = issue.get('message', 'No message')
            risk = issue.get('risk_level', 'unknown')
            risk_emoji = {'critical': '🔴', 'high': '🟠', 'medium': '🟡', 'low': '🟢'}.get(risk, '⚪')
            issues_list.append(f"- {risk_emoji} **{field}**: {message}")
        
        return f"""#### {flow_id}

**Flow Details:**
| Field | Value |
|-------|-------|
{chr(10).join(flow_details)}

**Security Issues:**
{chr(10).join(issues_list)}"""
    
    def _generate_flow_analysis(self) -> str:
        """Generate flow-by-flow analysis."""
        flow_results = self.results.get('flow_results', {})
        
        if not flow_results:
            return "## 📋 Flow Analysis\n\nNo flow data available for analysis."
        
        sections = ["## 📋 Flow Analysis"]
        
        # Create summary table
        table_rows = ["| Flow ID | Risk Level | Issues | Services | Ports | Actions |", 
                     "|---------|------------|--------|----------|-------|---------|"]
        
        for flow_id, flow_result in flow_results.items():
            flow_data = flow_result.get('flow_data', {})
            risk_level = flow_result.get('overall_risk', 'low')
            issues_count = len(flow_result.get('issues', []))
            
            risk_emoji = {'critical': '🔴', 'high': '🟠', 'medium': '🟡', 'low': '🟢'}.get(risk_level, '⚪')
            
            service = flow_data.get('Service', 'N/A')
            port = flow_data.get('Port', 'N/A')
            action = flow_data.get('Action', 'N/A')
            
            table_rows.append(f"| {flow_id} | {risk_emoji} {risk_level} | {issues_count} | {service} | {port} | {action} |")
        
        sections.append("\n".join(table_rows))
        
        return "\n\n".join(sections)
    
    def _generate_recommendations(self) -> str:
        """Generate security recommendations."""
        critical = self.results.get('critical_issues', 0)
        high = self.results.get('high_risk_issues', 0)
        medium = self.results.get('medium_risk_issues', 0)
        
        recommendations = ["## 💡 Security Recommendations"]
        
        if critical > 0:
            recommendations.append("""### 🚨 Immediate Actions Required
- **Block all critical-risk protocols** identified in this report
- **Review and update firewall rules** to prevent unauthorized access
- **Implement emergency incident response procedures**
- **Conduct immediate security assessment** of affected systems""")
        
        if high > 0:
            recommendations.append("""### ⚠️ High Priority Actions
- **Implement additional monitoring** for high-risk protocols
- **Enable multi-factor authentication** where applicable
- **Review access controls** and principle of least privilege
- **Consider protocol alternatives** where possible""")
        
        if medium > 0:
            recommendations.append("""### 📋 Medium Priority Actions
- **Plan migration** from deprecated protocols
- **Implement logging and monitoring** for concerning activities
- **Review and update security policies**
- **Schedule regular security assessments**""")
        
        # General recommendations
        recommendations.append("""### 🔄 Ongoing Security Practices
- **Regular validation** of network flow configurations
- **Automated security scanning** in CI/CD pipelines
- **Security awareness training** for development teams
- **Incident response plan** testing and updates""")
        
        return "\n\n".join(recommendations)
    
    def _generate_technical_details(self) -> str:
        """Generate technical implementation details."""
        errors = self.results.get('errors', [])
        
        sections = ["""## 🔧 Technical Details

### Validation Configuration
| Component | Status | Details |
|-----------|--------|---------|"""]
        
        # Check config files
        for config_type, config_path in self.config_files.items():
            status = "✅ Loaded" if os.path.exists(config_path) else "❌ Missing"
            sections.append(f"| {config_type} | {status} | `{config_path}` |")
        
        if errors:
            sections.append("### ⚠️ Validation Errors")
            for error in errors:
                sections.append(f"- {error}")
        
        sections.append(f"""### Environment Information
- **Validation Engine**: CSV Security Validator
- **CSV File**: `{self.csv_file}`
- **Report Generated**: {self.timestamp}
- **Total Flows Processed**: {self.results.get('total_flows', 0)}""")
        
        return "\n\n".join(sections)
    
    def _generate_appendix(self) -> str:
        """Generate appendix with raw data."""
        return f"""## 📎 Appendix

### Raw Validation Results
```json
{json.dumps(self.results, indent=2)}
```

### Configuration Files Used
{json.dumps(self.config_files, indent=2)}

---
*Report generated by CSV Security Validator - {self.timestamp}*"""


def generate_security_report(csv_file: str, unacceptable_file: str, guidance_file: str, 
                           output_file: str = "security_validation_report.md") -> str:
    """
    Generate a comprehensive security validation report.
    
    Args:
        csv_file: Path to CSV file
        unacceptable_file: Path to unacceptable values JSON
        guidance_file: Path to security guidance JSON
        output_file: Output markdown file path
        
    Returns:
        str: Path to generated report file
    """
    try:
        # Load and validate data
        flow_data = csv_to_dict_simple(csv_file)
        validator = SecurityValidator(unacceptable_file, guidance_file)
        results = validator.validate_flow_data(flow_data)
        
        # Generate report
        config_files = {
            "Unacceptable Values": unacceptable_file,
            "Security Guidance": guidance_file
        }
        
        generator = SecurityReportGenerator(results, csv_file, config_files)
        report_path = generator.generate_report(output_file)
        
        print(f"##[command]Security report generated: {report_path}")
        return report_path
        
    except Exception as e:
        print(f"##[error]Failed to generate security report: {e}")
        return None


def main():
    """Generate report for testing."""
    report_path = generate_security_report(
        csv_file="Example.csv",
        unacceptable_file="unacceptable_values.json",
        guidance_file="security_guidance.json",
        output_file="security_validation_report.md"
    )
    
    if report_path:
        print(f"Report generated successfully: {report_path}")
    else:
        print("Report generation failed")


if __name__ == "__main__":
    main()
