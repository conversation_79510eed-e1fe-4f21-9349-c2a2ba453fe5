#!/usr/bin/env python3
"""
Debug script to identify the source of the 'high_issues' error.
"""

from security_validator import csv_to_dict_simple, SecurityValidator

def debug_csv_loading(csv_file):
    """Debug CSV loading to see what flow IDs are generated."""
    
    print(f"🔍 Debugging CSV Loading: {csv_file}")
    print("=" * 60)
    
    try:
        # Load CSV data
        flow_data = csv_to_dict_simple(csv_file, expand_comma_separated=True)
        
        print(f"✅ CSV loaded successfully")
        print(f"📊 Total flows: {len(flow_data)}")
        print(f"📋 Flow IDs: {list(flow_data.keys())}")
        
        # Check for unusual flow IDs
        unusual_ids = []
        for flow_id in flow_data.keys():
            if not isinstance(flow_id, str):
                unusual_ids.append(f"Non-string ID: {flow_id} (type: {type(flow_id)})")
            elif not flow_id.startswith('flow_'):
                unusual_ids.append(f"Unusual format: {flow_id}")
            elif flow_id == 'high_issues':
                unusual_ids.append(f"Found problematic ID: {flow_id}")
        
        if unusual_ids:
            print(f"\n⚠️  Unusual Flow IDs Found:")
            for unusual_id in unusual_ids:
                print(f"   • {unusual_id}")
        else:
            print(f"\n✅ All flow IDs look normal")
        
        # Show first few flows
        print(f"\n📋 Sample Flow Data:")
        for i, (flow_id, flow_info) in enumerate(list(flow_data.items())[:3]):
            print(f"   {flow_id}: {flow_info}")
        
        return flow_data
        
    except Exception as e:
        print(f"❌ Error loading CSV: {e}")
        import traceback
        traceback.print_exc()
        return None

def debug_validation_process(csv_file):
    """Debug the full validation process."""
    
    print(f"\n🔧 Debugging Validation Process")
    print("=" * 50)
    
    try:
        validator = SecurityValidator('unacceptable_values.json', 'security_guidance.json')
        print(f"✅ Validator created")
        
        # Load flow data first
        flow_data = csv_to_dict_simple(csv_file, expand_comma_separated=True)
        if not flow_data:
            print(f"❌ No flow data loaded")
            return
        
        print(f"✅ Flow data loaded: {len(flow_data)} flows")
        
        # Try validating flow data directly
        print(f"\n🔍 Running validate_flow_data...")
        results = validator.validate_flow_data(flow_data)
        
        print(f"✅ Validation completed")
        print(f"📊 Results summary:")
        print(f"   Total flows: {results.get('total_flows', 0)}")
        print(f"   Flows with issues: {results.get('flows_with_issues', 0)}")
        print(f"   Critical issues: {results.get('critical_issues', 0)}")
        print(f"   High risk issues: {results.get('high_risk_issues', 0)}")
        print(f"   Medium risk issues: {results.get('medium_risk_issues', 0)}")
        print(f"   Low risk issues: {results.get('low_risk_issues', 0)}")
        print(f"   Errors: {len(results.get('errors', []))}")
        
        if results.get('errors'):
            print(f"\n❌ Errors found:")
            for error in results['errors']:
                print(f"   • {error}")
        
        return results
        
    except Exception as e:
        print(f"❌ Error in validation process: {e}")
        import traceback
        traceback.print_exc()
        return None

def check_json_files():
    """Check if JSON configuration files are present and valid."""
    
    print(f"\n📁 Checking JSON Configuration Files")
    print("=" * 45)
    
    import json
    import os
    
    files = ['unacceptable_values.json', 'security_guidance.json']
    
    for filename in files:
        print(f"\n🔍 Checking {filename}:")
        
        if not os.path.exists(filename):
            print(f"   ❌ File not found")
            continue
        
        try:
            with open(filename, 'r') as f:
                data = json.load(f)
            print(f"   ✅ Valid JSON")
            print(f"   📊 Keys: {list(data.keys())}")
            
            # Check for content
            if not data:
                print(f"   ⚠️  File is empty")
            else:
                print(f"   ✅ Has content")
                
        except json.JSONDecodeError as e:
            print(f"   ❌ Invalid JSON: {e}")
        except Exception as e:
            print(f"   ❌ Error reading: {e}")

def main():
    """Main debugging function."""
    
    print("🚨 'high_issues' Error Diagnostic Tool")
    print("=" * 70)
    
    # Get CSV file from user or use default
    import sys
    if len(sys.argv) > 1:
        csv_file = sys.argv[1]
    else:
        csv_file = input("Enter CSV file name (or press Enter for 'Example.csv'): ").strip()
        if not csv_file:
            csv_file = "Example.csv"
    
    print(f"🎯 Analyzing: {csv_file}")
    
    # Step 1: Check JSON files
    check_json_files()
    
    # Step 2: Debug CSV loading
    flow_data = debug_csv_loading(csv_file)
    
    # Step 3: Debug validation process
    if flow_data:
        debug_validation_process(csv_file)
    
    print(f"\n💡 Troubleshooting Tips:")
    print(f"   1. Check if 'high_issues' appears in your CSV file")
    print(f"   2. Verify CSV format and headers")
    print(f"   3. Ensure JSON configuration files exist and are valid")
    print(f"   4. Check for unusual characters in CSV data")
    print(f"   5. Try with a simple test CSV to isolate the issue")

if __name__ == "__main__":
    main()
